package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.FileInfoData;
import com.samsung.magicinfo.webauthor2.repository.model.FileInfoListData;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseFileInfoListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetContentFileListOpenApiMethod extends OpenApiMethod<List<String>, ResponseFileInfoListData> {
  private final String userId;
  
  private final String token;
  
  private final String contentId;
  
  public GetContentFileListOpenApiMethod(RestTemplate restTemplate, String userId, String token, String contentId) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.contentId = contentId;
  }
  
  protected final String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected final String getOpenApiMethodName() {
    return "getContentFileList";
  }
  
  protected final Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("contentId", this.contentId);
    return vars;
  }
  
  public final Class<ResponseFileInfoListData> getResponseClass() {
    return ResponseFileInfoListData.class;
  }
  
  public final List<String> convertResponseData(ResponseFileInfoListData responseFileInfoListData) {
    List<String> list = new ArrayList<>();
    FileInfoListData fileInfoListData = responseFileInfoListData.getResponseClass();
    List<FileInfoData> resultList = fileInfoListData.getResultList();
    for (FileInfoData fid : resultList)
      list.add(fid.getFileId()); 
    return list;
  }
}
