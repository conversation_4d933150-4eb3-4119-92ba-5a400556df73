package classes.com.samsung.magicinfo.webauthor2.repository.model.datalink;

import com.samsung.magicinfo.webauthor2.model.ConvertType;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertDataData;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;
import javax.xml.bind.annotation.XmlRootElement;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlRootElement(name = "ConvertTable")
@XmlAccessorType(XmlAccessType.FIELD)
public class ConvertTableData {
  @XmlElement(name = "convert_data_name")
  @XmlCDATA
  private String name;
  
  @XmlElement(name = "convert_type")
  private ConvertType convertType;
  
  @XmlElement(name = "create_date")
  private String tablecreatedDate;
  
  @XmlElementWrapper(name = "convertDataList")
  @XmlElement(name = "ConvertData")
  private List<ConvertDataData> convertDataList;
  
  public ConvertTableData() {}
  
  public ConvertTableData(String name, ConvertType convertType, String tablecreatedDate, List<ConvertDataData> convertDataList) {
    this.name = name;
    this.convertType = convertType;
    this.tablecreatedDate = tablecreatedDate;
    this.convertDataList = convertDataList;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public ConvertType getConvertType() {
    return this.convertType;
  }
  
  public void setConvertType(ConvertType convertType) {
    this.convertType = convertType;
  }
  
  public String getCreatedDate() {
    return this.tablecreatedDate;
  }
  
  public void setCreatedDate(String createdDate) {
    this.tablecreatedDate = createdDate;
  }
  
  public List<ConvertDataData> getConvertDataList() {
    return this.convertDataList;
  }
  
  public void setConvertDataList(List<ConvertDataData> convertDataList) {
    this.convertDataList = convertDataList;
  }
}
