package classes.com.samsung.magicinfo.webauthor2.model;

import com.google.common.base.Splitter;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import java.util.Iterator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class Video {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.model.Video.class);
  
  private Double duration;
  
  private int width;
  
  private int height;
  
  public static com.samsung.magicinfo.webauthor2.model.Video fromData(ContentData contentData) {
    Double duration = PlayTimeUtil.convertPlayTime(contentData.getPlayTime());
    int resolutionWidth = 10;
    int resolutionHeight = 10;
    if (contentData.getResolution() == null)
      return new com.samsung.magicinfo.webauthor2.model.Video(duration, resolutionWidth, resolutionHeight); 
    if (contentData.getResolution().isEmpty())
      return new com.samsung.magicinfo.webauthor2.model.Video(duration, resolutionWidth, resolutionHeight); 
    String sResolution = contentData.getResolution();
    String resolutionFormatSeparator = " x ";
    Iterator<String> sResolutionIterator = Splitter.on(" x ").split(sResolution).iterator();
    try {
      resolutionWidth = Integer.parseInt(sResolutionIterator.next());
      resolutionHeight = Integer.parseInt(sResolutionIterator.next());
    } catch (Exception e) {
      logger.error(e.getMessage());
    } 
    return new com.samsung.magicinfo.webauthor2.model.Video(duration, resolutionWidth, resolutionHeight);
  }
  
  public Video(int width, int height) {
    this.width = width;
    this.height = height;
  }
  
  public Video(Double duration, int width, int height) {
    this.duration = duration;
    this.width = width;
    this.height = height;
  }
  
  public Video() {}
  
  public Double getDuration() {
    return this.duration;
  }
  
  public int getWidth() {
    return this.width;
  }
  
  public int getHeight() {
    return this.height;
  }
}
