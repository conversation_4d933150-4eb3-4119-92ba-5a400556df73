package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.FileMetaInfo;
import com.samsung.magicinfo.webauthor2.service.FFmpegService;
import com.samsung.magicinfo.webauthor2.service.FileMetaService;
import com.samsung.magicinfo.webauthor2.util.OperatingSystem;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class FileMetaServiceImpl implements FileMetaService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.FileMetaServiceImpl.class);
  
  private FFmpegService ffmpegService;
  
  @Autowired
  public FileMetaServiceImpl(FFmpegService ffmpegService) {
    this.ffmpegService = ffmpegService;
  }
  
  public FileMetaInfo getFileMeta(Path file) {
    BufferedReader in = null;
    Process process = null;
    List<String> params = new ArrayList<>();
    FileMetaInfo fileMeta = new FileMetaInfo();
    if (Files.exists(file, new java.nio.file.LinkOption[0])) {
      try {
        if (OperatingSystem.isWindows()) {
          String ffmpegPath = this.ffmpegService.getFFmpeg().toAbsolutePath().toString();
          params.add(ffmpegPath);
        } else if (OperatingSystem.isLinux()) {
          params.add("ffmpeg");
        } 
        params.add("-i");
        params.add(file.toAbsolutePath().toString());
        process = (new ProcessBuilder(params)).start();
        in = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8));
        logger.info("Extract info from ffmpeg output.");
        String line;
        while ((line = in.readLine()) != null) {
          logger.debug(line);
          if (line.contains("Duration:")) {
            String[] splitStr = line.split(",");
            for (int i = 0; i < splitStr.length; i++);
            int location = splitStr[0].indexOf("Duration:");
            if (splitStr[0].length() > 18) {
              fileMeta.setLength(splitStr[0].substring(location + 10, splitStr[0].length()));
            } else {
              fileMeta.setLength("00:00:00");
            } 
          } 
          if (line.contains("Video:")) {
            String[] splitStr = line.split(", ");
            for (String information : splitStr) {
              Pattern pattern = Pattern.compile("\\d{2,}x\\d{2,}");
              Matcher matcher = pattern.matcher(information);
              int index = matcher.find() ? matcher.start() : -1;
              if (index > -1) {
                splitStr = information.split(" ");
                splitStr = splitStr[0].split("x");
                fileMeta.setWidth(splitStr[0]);
                fileMeta.setHeight(splitStr[1]);
              } 
            } 
          } 
        } 
        process.waitFor();
      } catch (IOException|InterruptedException ex) {
        logger.error(ex.getMessage(), ex);
      } finally {
        try {
          if (in != null)
            in.close(); 
          if (process != null)
            process.destroy(); 
        } catch (IOException e) {
          logger.error("cannot close input stream: {}", e.getMessage());
        } 
      } 
    } else {
      logger.error("File doesn't exist: " + file.toAbsolutePath());
    } 
    return fileMeta;
  }
}
