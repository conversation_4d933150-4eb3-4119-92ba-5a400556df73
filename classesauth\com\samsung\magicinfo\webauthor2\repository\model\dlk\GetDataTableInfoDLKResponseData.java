package classes.com.samsung.magicinfo.webauthor2.repository.model.dlk;

import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "replytabledata")
@XmlAccessorType(XmlAccessType.FIELD)
public class GetDataTableInfoDLKResponseData {
  @XmlElement(name = "tablename")
  private String tableName;
  
  @XmlElement(name = "colValue")
  private List<DLKTableRowData> rows;
  
  public String getTableName() {
    return this.tableName;
  }
  
  public void setTableName(String tableName) {
    this.tableName = tableName;
  }
  
  public List<DLKTableRowData> getRows() {
    return this.rows;
  }
  
  public void setRows(List<DLKTableRowData> rows) {
    this.rows = rows;
  }
}
