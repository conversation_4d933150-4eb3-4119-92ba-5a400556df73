package classes.com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.webapi.controller.ContentSaveController;
import com.samsung.magicinfo.webauthor2.webapi.resource.FileInfoResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class FileInfoResourceAssembler extends ResourceAssemblerSupport<FileInfo, FileInfoResource> {
  public FileInfoResourceAssembler() {
    super(ContentSaveController.class, FileInfoResource.class);
  }
  
  public FileInfoResourceAssembler(Class<?> controllerClass, Class<FileInfoResource> resourceType) {
    super(controllerClass, resourceType);
  }
  
  public FileInfoResource toResource(FileInfo t) {
    return new FileInfoResource(t, new org.springframework.hateoas.Link[0]);
  }
}
