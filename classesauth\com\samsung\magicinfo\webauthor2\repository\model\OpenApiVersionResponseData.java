package classes.com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class OpenApiVersionResponseData implements Serializable {
  private static final long serialVersionUID = 8703036396261503690L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement(name = "responseClass")
  private String openApiVersion;
  
  public String getCode() {
    return this.code;
  }
  
  public String getOpenApiVersion() {
    return this.openApiVersion;
  }
}
