package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.ContentNotFoundException;
import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class DeleteContentOpenApiMethod extends OpenApiMethod<String, OpenAPIResponseData> {
  private final String userId;
  
  private final String token;
  
  private final String contentId;
  
  public DeleteContentOpenApiMethod(RestTemplate restTemplate, String userId, String token, String contentId) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.contentId = contentId;
  }
  
  protected final String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected final String getOpenApiMethodName() {
    return "deleteContent";
  }
  
  protected final Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("contentId", this.contentId);
    return vars;
  }
  
  public final Class<OpenAPIResponseData> getResponseClass() {
    return OpenAPIResponseData.class;
  }
  
  public final String convertResponseData(OpenAPIResponseData responseData) {
    if (responseData.getErrorMessage() != null)
      throw new ContentNotFoundException(responseData.getCode(), responseData.getErrorMessage()); 
    return responseData.getResponseClass();
  }
}
