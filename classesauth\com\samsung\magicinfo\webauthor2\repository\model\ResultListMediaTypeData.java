package classes.com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class ResultListMediaTypeData implements Serializable {
  @XmlElement
  private Long totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "String")
  private List<String> resultList;
  
  public Long getTotalCount() {
    return this.totalCount;
  }
  
  public List<String> getResultList() {
    return this.resultList;
  }
}
