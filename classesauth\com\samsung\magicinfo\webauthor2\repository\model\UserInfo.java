package classes.com.samsung.magicinfo.webauthor2.repository.model;

import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class UserInfo implements Serializable {
  private static final long serialVersionUID = 8996668936902592747L;
  
  @XmlElement(name = "user_id")
  private String userId;
  
  @XmlElement(name = "root_group_id")
  private String rootGroupId;
  
  @XmlElement(name = "user_name")
  private String userName;
  
  @XmlElement(name = "email")
  private String email;
  
  @XmlElement(name = "organization")
  private String organization;
  
  @XmlElement(name = "team")
  private String team;
  
  @XmlElement(name = "job_position")
  private String jobPosition;
  
  @XmlElement(name = "phone_num")
  private String phoneNum;
  
  @XmlElement(name = "mobile_num")
  private String mobileNum;
  
  @XmlElement(name = "create_date")
  private String createDate;
  
  @XmlElement(name = "last_login_date")
  private String lastLoginDate;
  
  @XmlElement(name = "modify_date")
  private String modifyDate;
  
  @XmlElement(name = "group_id")
  private String groupId;
  
  @XmlElement(name = "isOrganization")
  private boolean isOrganization;
  
  @XmlElement(name = "group_name")
  private String groupName;
  
  @XmlElement(name = "role_name")
  private String roleName;
  
  @XmlElementWrapper(name = "ability")
  @XmlElement(name = "String")
  private List<String> list;
  
  public String getUserId() {
    return this.userId;
  }
  
  public String getRootGroupId() {
    return this.rootGroupId;
  }
  
  public String getUserName() {
    return this.userName;
  }
  
  public String getEmail() {
    return this.email;
  }
  
  public String getOrganization() {
    return this.organization;
  }
  
  public String getTeam() {
    return this.team;
  }
  
  public String getJobPosition() {
    return this.jobPosition;
  }
  
  public String getPhoneNum() {
    return this.phoneNum;
  }
  
  public String getMobileNum() {
    return this.mobileNum;
  }
  
  public String getCreateDate() {
    return this.createDate;
  }
  
  public String getLastLoginDate() {
    return this.lastLoginDate;
  }
  
  public String getModifyDate() {
    return this.modifyDate;
  }
  
  public String getGroupId() {
    return this.groupId;
  }
  
  public boolean isOrganization() {
    return this.isOrganization;
  }
  
  public String getGroupName() {
    return this.groupName;
  }
  
  public String getRoleName() {
    return this.roleName;
  }
  
  public List<String> getList() {
    return this.list;
  }
  
  public String toString() {
    return "UserInfo{userId='" + this.userId + '\'' + ", rootGroupId='" + this.rootGroupId + '\'' + ", userName='" + this.userName + '\'' + ", email='" + this.email + '\'' + ", organization='" + this.organization + '\'' + ", team='" + this.team + '\'' + ", jobPosition='" + this.jobPosition + '\'' + ", phoneNum='" + this.phoneNum + '\'' + ", mobileNum='" + this.mobileNum + '\'' + ", createDate='" + this.createDate + '\'' + ", lastLoginDate='" + this.lastLoginDate + '\'' + ", modifyDate='" + this.modifyDate + '\'' + ", groupId='" + this.groupId + '\'' + ", isOrganization=" + this.isOrganization + ", groupName='" + this.groupName + '\'' + ", roleName='" + this.roleName + '\'' + ", list=" + this.list + '}';
  }
}
