package classes.com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKDataType;
import com.samsung.magicinfo.webauthor2.model.datalink.TagList;
import com.samsung.magicinfo.webauthor2.model.datalink.Value;

public class StaticDLKData extends DLKData {
  private final Value value;
  
  private final TagList tagList;
  
  @JsonCreator
  public StaticDLKData(@JsonProperty("value") Value value, @JsonProperty("tagList") TagList tagList) {
    super(DLKDataType.Static);
    this.value = value;
    this.tagList = tagList;
  }
  
  public Value getValue() {
    return this.value;
  }
  
  public TagList getTagList() {
    return this.tagList;
  }
}
