package classes.com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.webapi.controller.ContentQueryController;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkDescriptorResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ControllerLinkBuilder;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class DataLinkDescriptorResourceAssembler extends ResourceAssemblerSupport<DataLinkDescriptor, DataLinkDescriptorResource> {
  public DataLinkDescriptorResourceAssembler() {
    super(DataLinkQueryController.class, DataLinkDescriptorResource.class);
  }
  
  public DataLinkDescriptorResource toResource(DataLinkDescriptor dataLinkDescriptor) {
    DataLinkDescriptorResource resource = new DataLinkDescriptorResource(dataLinkDescriptor, new org.springframework.hateoas.Link[0]);
    String lftContentId = dataLinkDescriptor.getLftContentId();
    resource.add(ControllerLinkBuilder.linkTo(((ContentQueryController)ControllerLinkBuilder.methodOn(ContentQueryController.class, new Object[0])).getContent(lftContentId)).withRel("lftContent"));
    return resource;
  }
}
