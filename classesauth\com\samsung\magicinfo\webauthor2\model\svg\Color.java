package classes.com.samsung.magicinfo.webauthor2.model.svg;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;

public class Color {
  private double alpha;
  
  private int blue;
  
  private int green;
  
  private int red;
  
  @JsonCreator
  public Color(@JsonProperty(value = "red", required = true) int red, @JsonProperty(value = "green", required = true) int green, @JsonProperty(value = "blue", required = true) int blue, @JsonProperty(value = "alpha", required = true) double alpha) {
    this.alpha = alpha;
    this.blue = blue;
    this.green = green;
    this.red = red;
  }
  
  public double getAlpha() {
    return this.alpha;
  }
  
  public void setAlpha(double alpha) {
    this.alpha = alpha;
  }
  
  public int getBlue() {
    return this.blue;
  }
  
  public void setBlue(int blue) {
    this.blue = blue;
  }
  
  public int getGreen() {
    return this.green;
  }
  
  public void setGreen(int green) {
    this.green = green;
  }
  
  public int getRed() {
    return this.red;
  }
  
  public void setRed(int red) {
    this.red = red;
  }
  
  public String toString() {
    return "rgba(" + this.red + "," + this.green + "," + this.blue + "," + this.alpha + ")";
  }
}
