package classes.com.samsung.magicinfo.webauthor2.xml.lfd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlRootElement(name = "FileItem")
@XmlAccessorType(XmlAccessType.NONE)
public class SupportFileItem {
  @XmlAttribute(name = "type")
  private String type;
  
  @XmlElement(name = "PureFileItem")
  @XmlCDATA
  private String pureFileItem;
  
  @XmlElement(name = "RealFullPath")
  @XmlCDATA
  private String realFullPath;
  
  @XmlElement(name = "FileSize")
  private int fileSize;
  
  @XmlElement(name = "FileID")
  private String fileId;
  
  @XmlElement(name = "FileHashValue")
  private String fileHashValue;
  
  @XmlElement(name = "KeyPathLocation")
  @XmlCDATA
  private String keyPathLocation;
  
  public SupportFileItem() {}
  
  public SupportFileItem(String type, String pureFileItem, String realFullPath, int fileSize, String fileId, String fileHashValue, String keyPathLocation) {
    this.type = type;
    this.pureFileItem = pureFileItem;
    this.realFullPath = realFullPath;
    this.fileSize = fileSize;
    this.fileId = fileId;
    this.fileHashValue = fileHashValue;
    this.keyPathLocation = keyPathLocation;
  }
  
  public String getType() {
    return this.type;
  }
  
  public void setType(String type) {
    this.type = type;
  }
  
  public String getPureFileItem() {
    return this.pureFileItem;
  }
  
  public void setPureFileItem(String pureFileItem) {
    this.pureFileItem = pureFileItem;
  }
  
  public String getRealFullPath() {
    return this.realFullPath;
  }
  
  public void setRealFullPath(String realFullPath) {
    this.realFullPath = realFullPath;
  }
  
  public int getFileSize() {
    return this.fileSize;
  }
  
  public void setFileSize(int fileSize) {
    this.fileSize = fileSize;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public String getFileHashValue() {
    return this.fileHashValue;
  }
  
  public void setFileHashValue(String fileHashValue) {
    this.fileHashValue = fileHashValue;
  }
  
  public String getKeyPathLocation() {
    return this.keyPathLocation;
  }
  
  public void setKeyPathLocation(String keyPathLocation) {
    this.keyPathLocation = keyPathLocation;
  }
  
  public String toString() {
    return "SupportFileItem{type='" + this.type + '\'' + ", pureFileItem='" + this.pureFileItem + '\'' + ", realFullPath='" + this.realFullPath + '\'' + ", fileSize='" + this.fileSize + '\'' + ", fileId='" + this.fileId + '\'' + ", fileHashValue='" + this.fileHashValue + '\'' + ", keyPathLocation='" + this.keyPathLocation + '\'' + '}';
  }
}
