package classes.com.samsung.magicinfo.webauthor2.service;

import java.nio.file.Path;

public interface RemoteContentService {
  byte[] getContentFileFromMagicInfoServer(String paramString1, String paramString2);
  
  String getVwlFileFromMagicInfoServer(String paramString1, String paramString2);
  
  String getXmlFileContents(String paramString1, String paramString2);
  
  Path getContentFileFromMagicInfoServer(Path paramPath, String paramString1, String paramString2);
  
  Path getContentFileFromMagicInfoServer(Path paramPath, String paramString1, String paramString2, String paramString3);
  
  Path getFontFileFromMagicInfoServer(String paramString1, String paramString2);
}
