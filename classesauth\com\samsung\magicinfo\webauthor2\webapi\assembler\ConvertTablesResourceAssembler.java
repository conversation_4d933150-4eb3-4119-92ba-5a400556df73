package classes.com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.ConvertTableResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class ConvertTablesResourceAssembler extends ResourceAssemblerSupport<ConvertTable, ConvertTableResource> {
  public ConvertTablesResourceAssembler() {
    super(DataLinkQueryController.class, ConvertTableResource.class);
  }
  
  public ConvertTableResource toResource(ConvertTable convertTable) {
    ConvertTableResource resource = new ConvertTableResource(convertTable, new org.springframework.hateoas.Link[0]);
    return resource;
  }
}
