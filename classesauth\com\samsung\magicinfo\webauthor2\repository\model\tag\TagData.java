package classes.com.samsung.magicinfo.webauthor2.repository.model.tag;

import javax.xml.bind.annotation.XmlElement;

public class TagData {
  @XmlElement(name = "tag_id")
  private int id;
  
  @XmlElement(name = "tag_name")
  private String name;
  
  @XmlElement(name = "tag_value")
  private String value;
  
  @XmlElement(name = "tag_desc")
  private String description;
  
  @XmlElement(name = "tag_organ")
  private Long organizationId;
  
  @XmlElement(name = "create_date")
  private String createdDate;
  
  @XmlElement(name = "tag_type")
  private int type;
  
  public int getId() {
    return this.id;
  }
  
  public String getName() {
    return this.name;
  }
  
  public String getValue() {
    return this.value;
  }
  
  public String getDescription() {
    return this.description;
  }
  
  public Long getOrganizationId() {
    return this.organizationId;
  }
  
  public String getCreated() {
    return this.createdDate;
  }
  
  public int getType() {
    return this.type;
  }
}
