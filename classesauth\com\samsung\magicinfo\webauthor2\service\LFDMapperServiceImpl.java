package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.VerificationResponse;
import com.samsung.magicinfo.webauthor2.service.LFDMapperService;
import java.io.File;
import java.io.IOException;
import java.io.StringReader;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import javax.xml.transform.Source;
import javax.xml.transform.stream.StreamSource;
import javax.xml.validation.Schema;
import javax.xml.validation.SchemaFactory;
import javax.xml.validation.Validator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXParseException;

@Service
public class LFDMapperServiceImpl implements LFDMapperService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.LFDMapperServiceImpl.class);
  
  private static final String LFD_SCHEME_LOCATION = "scheme/LFDFormatDefinition.xsd";
  
  private List<String> tagsForNotification = new ArrayList<>();
  
  public boolean validate(String inputXml) {
    VerificationResponse response = validateWithResponse(inputXml);
    return response.isValid();
  }
  
  public VerificationResponse validateWithResponse(String inputXml) {
    try {
      SchemaFactory factory = SchemaFactory.newInstance("http://www.w3.org/XML/XMLSchema/v1.1");
      String fullFeature = "http://apache.org/xml/features/validation/cta-full-xpath-checking";
      factory.setFeature("http://apache.org/xml/features/validation/cta-full-xpath-checking", true);
      File schemaFile = getLFDSchemaFile();
      Schema schema = factory.newSchema(schemaFile);
      Validator validator = schema.newValidator();
      Source source = new StreamSource(new StringReader(inputXml));
      List<SAXParseException> exceptions = new LinkedList<>();
      validator.setErrorHandler(getErrorHandler(exceptions));
      validator.validate(source);
      if (exceptions.isEmpty()) {
        logger.info("Xml is valid");
        return new VerificationResponse(true, "Valid");
      } 
      if (needToNotify(exceptions).booleanValue())
        return new VerificationResponse(false, "InValid"); 
      return new VerificationResponse(true, "Ignored");
    } catch (IOException|org.xml.sax.SAXException ex) {
      logger.error("Catched from LFD Validation: " + ex.getMessage());
      return new VerificationResponse(true, ex.getMessage());
    } 
  }
  
  private List<String> getTagsForNotification() {
    if (this.tagsForNotification.isEmpty())
      this.tagsForNotification.add("'ScriptFunctions'"); 
    return this.tagsForNotification;
  }
  
  private Boolean needToNotify(List<SAXParseException> exceptions) {
    List<String> tagsForNotification = getTagsForNotification();
    for (SAXParseException exception : exceptions) {
      String message = exception.getMessage();
      for (String tag : tagsForNotification) {
        if (message.toLowerCase().contains(tag.toLowerCase())) {
          StringBuilder sb = new StringBuilder();
          sb.append("Error in line:");
          sb.append(exception.getLineNumber());
          sb.append(" col:");
          sb.append(exception.getColumnNumber());
          sb.append(". Cause: ");
          sb.append(exception.getMessage());
          sb.append("\n");
          logger.info("Xml has issued tag, " + sb.toString());
          return Boolean.valueOf(true);
        } 
      } 
    } 
    return Boolean.valueOf(false);
  }
  
  private ErrorHandler getErrorHandler(List<SAXParseException> exceptions) {
    return (ErrorHandler)new Object(this, exceptions);
  }
  
  private File getLFDSchemaFile() throws IOException {
    ClassPathResource schema = new ClassPathResource("scheme/LFDFormatDefinition.xsd");
    return schema.getFile();
  }
}
