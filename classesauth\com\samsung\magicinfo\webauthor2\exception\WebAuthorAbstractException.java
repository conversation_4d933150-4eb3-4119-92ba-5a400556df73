package classes.com.samsung.magicinfo.webauthor2.exception;

public abstract class WebAuthorAbstractException extends RuntimeException {
  private static final long serialVersionUID = 6254760031554812650L;
  
  private int errorCode = -1;
  
  public WebAuthorAbstractException(String message) {
    super(message);
  }
  
  public WebAuthorAbstractException(int errorCode, String message) {
    super(message);
    this.errorCode = errorCode;
  }
  
  public int getErrorCode() {
    return this.errorCode;
  }
}
