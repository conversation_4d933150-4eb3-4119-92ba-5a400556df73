package classes.com.samsung.magicinfo.webauthor2.xml.datalink;

import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TagListType", propOrder = {"tagList", "column"})
public class TagListType {
  @XmlElement(name = "Tag", required = true)
  private List<String> tagList;
  
  @XmlElement(name = "Column")
  private String column;
  
  @XmlAttribute
  private String matchType;
  
  public List<String> getTagList() {
    return this.tagList;
  }
  
  public void setTagList(List<String> tagList) {
    this.tagList = tagList;
  }
  
  public String getMatchType() {
    return this.matchType;
  }
  
  public void setMatchType(String matchType) {
    this.matchType = matchType;
  }
  
  public String getColumn() {
    return this.column;
  }
  
  public void setColumn(String column) {
    this.column = column;
  }
}
