package classes.com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.DataLinkTableRow;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkTableRowResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class DataLinkTableRowResourceAssembler extends ResourceAssemblerSupport<DataLinkTableRow, DataLinkTableRowResource> {
  public DataLinkTableRowResourceAssembler() {
    super(DataLinkQueryController.class, DataLinkTableRowResource.class);
  }
  
  public DataLinkTableRowResource toResource(DataLinkTableRow dataLinkTableRow) {
    DataLinkTableRowResource resource = new DataLinkTableRowResource(dataLinkTableRow, new org.springframework.hateoas.Link[0]);
    return resource;
  }
}
