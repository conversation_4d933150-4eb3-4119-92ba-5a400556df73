package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import java.util.List;

public interface OpenAPIConvertTableRepository {
  List<ConvertTableData> getConvertTableDataList();
  
  String addConvertTableData(ConvertTableData paramConvertTableData);
  
  String deleteConvertTable(String paramString);
  
  String modifyConvertTableData(ConvertTableData paramConvertTableData1, ConvertTableData paramConvertTableData2);
}
