package classes.com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.webapi.controller.ContentGroupController;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentGroupResource;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.ResourceAssemblerSupport;
import org.springframework.stereotype.Component;

@Component
public class ContentGroupResourceAssembler extends ResourceAssemblerSupport<ContentGroup, ContentGroupResource> {
  public ContentGroupResourceAssembler() {
    super(ContentGroupController.class, ContentGroupResource.class);
  }
  
  public ContentGroupResource toResource(ContentGroup contentGroup) {
    ContentGroupResource resource = new ContentGroupResource(contentGroup, new org.springframework.hateoas.Link[0]);
    return resource;
  }
}
