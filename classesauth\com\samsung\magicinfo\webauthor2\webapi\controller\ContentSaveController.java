package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.CidMappingResponse;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.NewFileInfo;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import com.samsung.magicinfo.webauthor2.service.ContentSaveService;
import com.samsung.magicinfo.webauthor2.service.FileHashService;
import com.samsung.magicinfo.webauthor2.service.UploadFileNameValidationService;
import com.samsung.magicinfo.webauthor2.service.upload.ContentMIPUploadService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.ListIterator;
import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Scope("session")
@RequestMapping({"/save"})
public class ContentSaveController {
  private ContentSaveService contentSaveService;
  
  private CidMappingService cidMappingService;
  
  private FileHashService fileHashService;
  
  private ContentMIPUploadService uploadService;
  
  private ContentSaveElements contentSaveElements;
  
  private final UploadFileNameValidationService uploadFileNameValidationService;
  
  @Autowired
  public ContentSaveController(ContentSaveService contentSaveService, CidMappingService cidMappingService, FileHashService fileHashService, ContentMIPUploadService uploadService, ContentSaveElements contentSaveElements, UploadFileNameValidationService uploadFileNameValidationService) {
    this.contentSaveService = contentSaveService;
    this.cidMappingService = cidMappingService;
    this.fileHashService = fileHashService;
    this.uploadService = uploadService;
    this.contentSaveElements = contentSaveElements;
    this.uploadFileNameValidationService = uploadFileNameValidationService;
  }
  
  @GetMapping({"/cid"})
  public HttpEntity<CidMappingResponse> getContentId(@RequestParam(required = false) String contentId) {
    return (HttpEntity<CidMappingResponse>)ResponseEntity.ok(new CidMappingResponse(this.cidMappingService.cidMapping(contentId)));
  }
  
  @PostMapping({"/fileItems"})
  public HttpEntity<List<MediaSource>> mapFileItems(@RequestBody FileItemsDescriptor fileItemsDescriptor, HttpServletRequest request) {
    if (false == validateFileNameNotToMoveIntoUpperFolder(fileItemsDescriptor.getMediaSources()).booleanValue()) {
      List<MediaSource> empty = new ArrayList<>();
      return (HttpEntity<List<MediaSource>>)ResponseEntity.badRequest().body(empty);
    } 
    this.contentSaveService.saveProjectProperties(fileItemsDescriptor);
    List<MediaSource> filledMediaSources = this.contentSaveService.getFilledMediaSources(fileItemsDescriptor, request);
    List<MediaSource> updatedMediaSources = this.contentSaveService.getUpdatedMediaSources(filledMediaSources);
    return (HttpEntity<List<MediaSource>>)ResponseEntity.ok(updatedMediaSources);
  }
  
  @PostMapping({"/newFile"})
  public HttpEntity<MediaSource> createNewSupportFileItem(@RequestBody NewFileInfo fileItemsDescriptor) throws IOException {
    MediaSource mediaSource = this.contentSaveService.getMediaSourceFromNewFile(fileItemsDescriptor);
    return (HttpEntity<MediaSource>)ResponseEntity.ok(mediaSource);
  }
  
  @PostMapping({"/xml"})
  public HttpEntity<UploadResponse> xmlUpload(@RequestParam String xml) throws UploaderException {
    this.fileHashService.fileHashRefresh(xml);
    String savedProjectContentId = this.uploadService.upload(this.contentSaveElements);
    UploadResponse response = new UploadResponse(200, savedProjectContentId);
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(response);
  }
  
  @ExceptionHandler({WebAuthorAbstractException.class})
  public ResponseEntity<UploadResponse> uploaderExceptionHandler(WebAuthorAbstractException ex) {
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
      .body(new UploadResponse(ex.getErrorCode(), ex.getMessage()));
  }
  
  private Boolean validateFileNameNotToMoveIntoUpperFolder(List<MediaSource> mediaSources) {
    ListIterator<MediaSource> iter = mediaSources.listIterator();
    while (iter.hasNext()) {
      MediaSource ms = iter.next();
      if (false == this.uploadFileNameValidationService.validateFileNameNotToMoveIntoUpperFolder(ms.getFileName()).booleanValue())
        return Boolean.valueOf(false); 
    } 
    return Boolean.valueOf(true);
  }
}
