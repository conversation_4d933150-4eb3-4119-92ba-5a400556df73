package classes.com.samsung.magicinfo.webauthor2.service.upload;

import com.google.common.base.Strings;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.ImageDimension;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.repository.FileHashRefreshRepository;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import com.samsung.magicinfo.webauthor2.service.upload.HttpContentUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.JobStateService;
import com.samsung.magicinfo.webauthor2.service.upload.UploadHelperService;
import com.samsung.magicinfo.webauthor2.service.upload.WebContentUploadService;
import com.samsung.magicinfo.webauthor2.util.FileHashUtil;
import com.samsung.magicinfo.webauthor2.util.ImageDimensionUtil;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;
import com.samsung.magicinfo.webauthor2.util.SupportedFormatUtils;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFileResponseType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class WebContentUploadServiceImpl implements WebContentUploadService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.upload.WebContentUploadServiceImpl.class);
  
  private CidMappingService cidMappingService;
  
  private CSDFileService csdFileService;
  
  private FileHashRefreshRepository fileHashRefreshRepository;
  
  private UploadHelperService uploadHelperService;
  
  private HttpContentUploadService httpContentUploadService;
  
  private JobStateService jobStateService;
  
  private ServletContext servletContext;
  
  private MultipartFilenameValidator multipartFilenameValidator;
  
  private UserData userData;
  
  private ContentSaveElements contentSaveElements;
  
  private static final String DEFAULT_FILE_ID = "00000000-0000-0000-0000-000000000000";
  
  @Autowired
  public WebContentUploadServiceImpl(CidMappingService cidMappingService, CSDFileService csdFileService, FileHashRefreshRepository fileHashRefreshRepository, UploadHelperService uploadHelperService, HttpContentUploadService httpContentUploadService, JobStateService jobStateService, ServletContext servletContext, MultipartFilenameValidator multipartFilenameValidator, UserData userData, ContentSaveElements contentSaveElements) {
    this.cidMappingService = cidMappingService;
    this.csdFileService = csdFileService;
    this.fileHashRefreshRepository = fileHashRefreshRepository;
    this.uploadHelperService = uploadHelperService;
    this.httpContentUploadService = httpContentUploadService;
    this.jobStateService = jobStateService;
    this.servletContext = servletContext;
    this.multipartFilenameValidator = multipartFilenameValidator;
    this.userData = userData;
    this.contentSaveElements = contentSaveElements;
  }
  
  public MediaSource setWebContentThumbnail(MultipartFile thumbnail) throws IOException, FileItemValidationException {
    String contentId = this.cidMappingService.cidMapping();
    MediaSource msThumbnail = storeFileItem(contentId, "", thumbnail);
    saveAsProjectThumbnail(msThumbnail);
    return msThumbnail;
  }
  
  public MediaSource setWebContentThumbnail(String thumbFilePath) throws IOException, FileItemValidationException {
    String contentId = this.cidMappingService.cidMapping();
    MediaSource msThumbnail = storeFileItem(contentId, "", thumbFilePath);
    saveAsProjectThumbnail(msThumbnail);
    return msThumbnail;
  }
  
  public String initializeFileUploadProcess(String contentId, String contentName, String startupPage) throws IOException, FileItemValidationException {
    String cid = this.cidMappingService.cidMapping(contentId);
    this.contentSaveElements.setProjectName(contentName + ".LFD");
    this.contentSaveElements.setPlayerType(DeviceType.S4PLAYER);
    this.contentSaveElements.setMediaSources(new ArrayList());
    MediaSource lfdMS = new MediaSource();
    lfdMS.setContentId(cid);
    lfdMS.setTitle(contentName);
    lfdMS.setStartupPage(startupPage);
    List<MediaSource> mediaSources = new ArrayList<>();
    mediaSources.add(lfdMS);
    this.contentSaveElements.setMediaSources(mediaSources);
    return cid;
  }
  
  public MediaSource storeSupportFileItem(String cid, String path, MultipartFile file) throws IOException, FileItemValidationException {
    MediaSource mediaSource = storeFileItem(cid, path, file);
    mediaSource.setData(pathStringFormatForCSD(path));
    mediaSource.setFileType("content");
    mediaSource.setSupportFileItem(true);
    this.contentSaveElements.getMediaSources().add(mediaSource);
    return mediaSource;
  }
  
  public MediaSource storeSupportFileItem(String cid, String path, String file) throws IOException, FileItemValidationException {
    MediaSource mediaSource = storeFileItem(cid, path, file);
    mediaSource.setData(pathStringFormatForCSD(path));
    mediaSource.setFileType("content");
    mediaSource.setSupportFileItem(true);
    this.contentSaveElements.getMediaSources().add(mediaSource);
    return mediaSource;
  }
  
  public List<MediaSource> getUpdatedMediaSources(FileItemsDescriptor fileItemsDescriptor) {
    this.contentSaveElements.setXml(fileItemsDescriptor.getXml());
    this.contentSaveElements.setHeight(fileItemsDescriptor.getHeight());
    this.contentSaveElements.setWidth(fileItemsDescriptor.getWidth());
    this.contentSaveElements.setPlayTime(PlayTimeUtil.covertPlayTimeFromSeconds(fileItemsDescriptor.getPlayTime()));
    fillLfdInfo(this.contentSaveElements.getMediaSources().get(0));
    List<MediaSource> sourcesWithThumbnail = this.contentSaveElements.getMediaSources();
    sourcesWithThumbnail.add(getThumbnailMediaSource());
    String csdXml = this.csdFileService.generateCSD(sourcesWithThumbnail, this.contentSaveElements.getProjectContentId(), this.contentSaveElements.getPlayerType());
    TransferFilesResponseType csdResponse = this.csdFileService.postProjectCsdToMips(csdXml, this.contentSaveElements.getProjectContentId(), null);
    logger.debug("CSD : " + csdXml);
    logger.debug("CSD RESPONSE, CID : " + csdResponse.getContentId());
    for (TransferFileResponseType fs : csdResponse.getTransferFiles())
      logger.debug("CSD FILE : IsNew - " + fs.isNew() + ", Idx - " + fs.getReqIndex() + ", FileID - " + fs.getFileId()); 
    List<MediaSource> updatedMediaSources = this.csdFileService.updateMediaSources(this.contentSaveElements.getMediaSources(), csdResponse);
    this.contentSaveElements.setMediaSources(updatedMediaSources);
    return updatedMediaSources;
  }
  
  public String validateFileName(String name) {
    return this.multipartFilenameValidator.validateName(name);
  }
  
  private void saveAsProjectThumbnail(MediaSource ms) {
    ms.setFileType("thumbnail");
    ms.setFileId("00000000-0000-0000-0000-000000000000");
    this.contentSaveElements.setProjectThumbnailMediaSource(ms);
    this.contentSaveElements.setPathTothumbnail(ms.getPath().toString());
  }
  
  private MediaSource storeFileItem(String cid, String path, MultipartFile file) throws IOException, FileItemValidationException {
    this.multipartFilenameValidator.validateSupportFileItem(file);
    Path fileUploaded = getFileFromMultipartFile(cid, path, file);
    MediaSource mediaSource = this.uploadHelperService.getDetailsFromFile(fileUploaded);
    return mediaSource;
  }
  
  private MediaSource storeFileItem(String cid, String path, String file) throws IOException, FileItemValidationException {
    Path importedFilePath = Paths.get(this.servletContext.getRealPath(file), new String[0]);
    MediaSource mediaSource = this.uploadHelperService.getDetailsFromFile(importedFilePath);
    return mediaSource;
  }
  
  private Path createDirectoryStructure(String relativePath) {
    try {
      String previewPath = this.servletContext.getRealPath("preview");
      Path workingDirectory = Paths.get(previewPath, new String[] { relativePath });
      if (Files.notExists(workingDirectory, new java.nio.file.LinkOption[0]))
        Files.createDirectories(workingDirectory, (FileAttribute<?>[])new FileAttribute[0]); 
      return workingDirectory;
    } catch (IOException e) {
      throw new UploaderException(500, "Error during file structure initialization");
    } 
  }
  
  private Path getFileFromMultipartFile(String cid, String path, MultipartFile multipartFile) throws IOException {
    if (Strings.isNullOrEmpty(path) || path.equals("/"))
      path = ""; 
    if (path.length() > 1 && !path.startsWith("/"))
      path = "/" + path; 
    Path workingDirectory = createDirectoryStructure(cid + path);
    Path filePath = Paths.get(workingDirectory.toString(), new String[] { multipartFile.getOriginalFilename() });
    File file = filePath.toFile();
    FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
    return filePath;
  }
  
  public List<MediaSource> getUpdatedMediaSources(MultipartFile zip, String contentId, String contentName, DeviceType deviceType, String startupPage, String contentWidth, String contentHeight, int lfdSizeExceptMediaFileSizes) {
    List<MediaSource> zipMediaSource = getMediaSourceFromMultipartFile(zip, contentName, startupPage, contentWidth, contentHeight, lfdSizeExceptMediaFileSizes);
    List<MediaSource> updatedMediaSources = createMediaSourcesWithZip(zipMediaSource, contentId, deviceType, contentWidth, contentHeight);
    return updatedMediaSources;
  }
  
  public List<MediaSource> getUpdatedMediaSources(String zipFilePath, String contentId, String contentName, DeviceType deviceType, String startupPage, String contentWidth, String contentHeight, int lfdSizeExceptMediaFileSizes) {
    List<MediaSource> zipMediaSource = getMediaSourceFromFile(zipFilePath, contentName, startupPage, contentWidth, contentHeight, lfdSizeExceptMediaFileSizes);
    List<MediaSource> updatedMediaSources = createMediaSourcesWithZip(zipMediaSource, contentId, deviceType, contentWidth, contentHeight);
    return updatedMediaSources;
  }
  
  public String uploadWebContent(String lfdXml) {
    List<MediaSource> updatedMediaSources = this.contentSaveElements.getMediaSources();
    MediaSource mediaSourceXml = updatedMediaSources.get(0);
    String contentId = mediaSourceXml.getContentId();
    try {
      Path lfdFilePath = Paths.get(mediaSourceXml.getPath(), new String[0]);
      File lfdFile = lfdFilePath.toFile();
      Files.deleteIfExists(lfdFilePath);
      FileUtils.writeStringToFile(lfdFile, lfdXml, StandardCharsets.UTF_8);
      String newHash = FileHashUtil.getHash(lfdFile);
      mediaSourceXml.setFileHash(newHash);
      this.fileHashRefreshRepository.fileHashRefresh(contentId, mediaSourceXml.getFileId(), newHash);
      this.httpContentUploadService.uploadListOfMediaSources(updatedMediaSources, contentId);
      boolean contentIsDuplicate = false;
      this.contentSaveElements.setProjectThumbnailMediaSource(new MediaSource());
      this.jobStateService.jobStateSuccess(this.userData.getUserId(), this.userData.getToken(), contentId, "1", contentIsDuplicate);
    } catch (Exception e) {
      logger.error("Failed to upload WebContent : " + e.getMessage());
      this.jobStateService.jobStateFail(this.userData.getUserId(), this.userData.getToken(), contentId, "1");
      throw new UploaderException("Failed to upload WebContent " + e.getMessage());
    } 
    return contentId;
  }
  
  private List<MediaSource> createMediaSourcesWithZip(List<MediaSource> zipMediaSource, String contentId, DeviceType deviceType, String contentWidth, String contentHeight) {
    String cid = this.cidMappingService.cidMapping(contentId);
    String csdXml = this.csdFileService.generateCSD(zipMediaSource, cid, deviceType);
    TransferFilesResponseType csdResponse = this.csdFileService.postSingleFileCsdToMips(csdXml, cid);
    logger.debug("CSD : " + csdXml);
    logger.debug("CSD RESPONSE, CID : " + csdResponse.getContentId());
    for (TransferFileResponseType fs : csdResponse.getTransferFiles())
      logger.debug("CSD FILE : IsNew - " + fs.isNew() + ", Idx - " + fs.getReqIndex() + ", FileID - " + fs.getFileId()); 
    List<MediaSource> updatedMediaSources = this.csdFileService.updateMediaSources(zipMediaSource, csdResponse);
    ((MediaSource)updatedMediaSources.get(0)).setContentId(cid);
    this.contentSaveElements.setMediaSources(updatedMediaSources);
    this.contentSaveElements.setWidth(Integer.parseInt(contentWidth));
    this.contentSaveElements.setHeight(Integer.parseInt(contentHeight));
    return updatedMediaSources;
  }
  
  private List<MediaSource> getMediaSourceFromMultipartFile(MultipartFile zip, String contentName, String startupPage, String contentWidth, String contentHeight, int lfdSizeExceptMediaFileSizes) {
    List<MediaSource> mediaSourcesToUpload = new ArrayList<>();
    try {
      String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
      String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
      MediaSource zipFile = getZipMediaSource(zip, contentName, serverDirectoryPath, userWorkspaceDirectory);
      MediaSource xmlFile = getLfdMediaSource(contentName, serverDirectoryPath, userWorkspaceDirectory, startupPage, zipFile, contentWidth, contentHeight, lfdSizeExceptMediaFileSizes);
      MediaSource thumbnailFile = getThumbnailMediaSource();
      mediaSourcesToUpload.add(xmlFile);
      mediaSourcesToUpload.add(zipFile);
      mediaSourcesToUpload.add(thumbnailFile);
      return mediaSourcesToUpload;
    } catch (IOException e) {
      logger.error(e.getMessage());
      return Collections.emptyList();
    } 
  }
  
  private List<MediaSource> getMediaSourceFromFile(String zipFileRelativePath, String contentName, String startupPage, String contentWidth, String contentHeight, int lfdSizeExceptMediaFileSizes) {
    List<MediaSource> mediaSourcesToUpload = new ArrayList<>();
    try {
      String serverDirectoryPath = this.servletContext.getRealPath("insertContents");
      String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
      MediaSource zipFile = getZipMediaSource(zipFileRelativePath, contentName, serverDirectoryPath, userWorkspaceDirectory);
      MediaSource xmlFile = getLfdMediaSource(contentName, serverDirectoryPath, userWorkspaceDirectory, startupPage, zipFile, contentWidth, contentHeight, lfdSizeExceptMediaFileSizes);
      MediaSource thumbnailFile = getThumbnailMediaSource();
      mediaSourcesToUpload.add(xmlFile);
      mediaSourcesToUpload.add(zipFile);
      mediaSourcesToUpload.add(thumbnailFile);
      return mediaSourcesToUpload;
    } catch (IOException e) {
      logger.error(e.getMessage());
      return Collections.emptyList();
    } 
  }
  
  private MediaSource getThumbnailMediaSource() {
    MediaSource thumbnail = this.contentSaveElements.getProjectThumbnailMediaSource();
    if (!Strings.isNullOrEmpty(thumbnail.getFileName()))
      return thumbnail; 
    String name = "HTML_THUMBNAIL.PNG";
    Path thumbnailFilePath = Paths.get(this.servletContext.getRealPath("images"), new String[] { name });
    File thumbnailFile = new File(thumbnailFilePath.toString());
    MediaSource msThumbnail = new MediaSource();
    msThumbnail.setData("");
    msThumbnail.setFileName(name);
    String extension = FilenameUtils.getExtension(name);
    msThumbnail.setMediaType(SupportedFormatUtils.getMediaTypeForExtension(extension));
    msThumbnail.setFileType("thumbnail");
    msThumbnail.setFileHash(FileHashUtil.getHash(thumbnailFile));
    msThumbnail.setMediaSize(thumbnailFile.length());
    fillImageDimensions(msThumbnail, thumbnailFile);
    msThumbnail.setPath(thumbnailFilePath.toString());
    msThumbnail.setFileId(UUID.randomUUID().toString().toUpperCase());
    return msThumbnail;
  }
  
  private MediaSource getZipMediaSource(MultipartFile zip, String contentName, String serverDirectoryPath, String userWorkspaceDirectory) throws IOException {
    Path zipFilePath = Paths.get(serverDirectoryPath, new String[] { userWorkspaceDirectory, zip.getOriginalFilename() });
    FileUtils.copyInputStreamToFile(zip.getInputStream(), zipFilePath.toFile());
    File zipFile = zipFilePath.toFile();
    MediaSource msZip = createMediaSourceZip(contentName, zipFilePath, zipFile);
    return msZip;
  }
  
  private MediaSource getZipMediaSource(String zipFileRelativePath, String contentName, String serverDirectoryPath, String userWorkspaceDirectory) throws IOException {
    Path zipFilePath = Paths.get(this.servletContext.getRealPath(zipFileRelativePath), new String[0]);
    File zipFile = zipFilePath.toFile();
    MediaSource msZip = createMediaSourceZip(contentName, zipFilePath, zipFile);
    return msZip;
  }
  
  private MediaSource createMediaSourceZip(String contentName, Path filePath, File file) throws IOException {
    MediaSource msZip = new MediaSource();
    msZip.setTitle(contentName);
    msZip.setFileName(file.getName());
    msZip.setFileType(FilenameUtils.getExtension(file.getName()));
    msZip.setMediaType(MediaType.HTML);
    msZip.setMediaSize(Files.size(filePath));
    msZip.setFileId(UUID.randomUUID().toString().toUpperCase());
    msZip.setMediaWidth(1920);
    msZip.setMediaHeight(1080);
    msZip.setFileHash(FileHashUtil.getHash(file));
    msZip.setPath(filePath.toString());
    return msZip;
  }
  
  private String pathStringFormatForCSD(String path) {
    if (Strings.isNullOrEmpty(path) || path.equals("/"))
      return ".\\"; 
    if (path.length() > 1 && !path.startsWith("/"))
      path = "/" + path; 
    if (path.length() > 1 && !path.endsWith("/"))
      path = path + "/"; 
    return "." + path.replace("/", "\\");
  }
  
  private MediaSource getLfdMediaSource(String contentName, String serverDirectoryPath, String userWorkspaceDirectory, String startupPage, MediaSource zipFile, String contentWidth, String contentHeight, int lfdSizeExceptMediaFileSizes) throws IOException {
    Path lfdFilePath = Paths.get(serverDirectoryPath, new String[] { userWorkspaceDirectory, contentName + ".lfd" });
    File lfdFile = lfdFilePath.toFile();
    Files.deleteIfExists(lfdFilePath);
    FileUtils.writeStringToFile(lfdFile, "<?xml version=\"1.0\" encoding=\"utf-8\"?>", StandardCharsets.UTF_8);
    MediaSource xmlFile = new MediaSource();
    xmlFile.setTitle(contentName);
    xmlFile.setFileName(lfdFile.getName());
    xmlFile.setFileType(FilenameUtils.getExtension(lfdFile.getName()));
    xmlFile.setMediaType(MediaType.HTML);
    xmlFile.setFileId(UUID.randomUUID().toString().toUpperCase());
    xmlFile.setMediaWidth(Integer.parseInt(contentWidth));
    xmlFile.setMediaHeight(Integer.parseInt(contentHeight));
    xmlFile.setMediaDuration(PlayTimeUtil.convertPlayTime("60000").doubleValue());
    xmlFile.setPath(lfdFilePath.toString());
    xmlFile.setFileHash(FileHashUtil.getHash(lfdFile));
    xmlFile.setMediaSize(Files.size(lfdFilePath));
    xmlFile.setStartupPage(startupPage);
    xmlFile.setMediaSize(getLfdSizeUpdatedMediaFileSizes(zipFile, lfdSizeExceptMediaFileSizes));
    return xmlFile;
  }
  
  public void fillLfdInfo(MediaSource mediaSource) {
    try {
      String xml = this.contentSaveElements.getXml();
      Path xmlPath = writeXmlToFile(this.contentSaveElements.getProjectName(), xml);
      mediaSource.setPath(xmlPath.toString());
      fillHash(mediaSource, xmlPath);
      fillLength(mediaSource, xmlPath);
      mediaSource.setFileName(this.contentSaveElements.getProjectName());
      mediaSource.setMediaWidth(this.contentSaveElements.getWidth());
      mediaSource.setMediaHeight(this.contentSaveElements.getHeight());
      mediaSource.setMediaDuration(PlayTimeUtil.convertPlayTime(this.contentSaveElements.getPlayTime()).doubleValue());
      mediaSource.setFileType("lfd");
      mediaSource.setMediaType(MediaType.HTML);
      if (Strings.isNullOrEmpty(mediaSource.getFileId()))
        mediaSource.setFileId("00000000-0000-0000-0000-000000000000"); 
    } catch (IOException e) {
      logger.error("Error during setting xml media source properties: id {}", mediaSource.getContentId());
    } 
  }
  
  private Path writeXmlToFile(String projectName, String xml) throws IOException {
    String insertContents = this.servletContext.getRealPath("insertContents");
    String userWorkspaceDirectory = this.userData.getWorkspaceFolderName();
    Path xmlPath = Paths.get(insertContents, new String[] { userWorkspaceDirectory, projectName });
    if (Files.exists(xmlPath, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(xmlPath.toFile()); 
    FileUtils.writeStringToFile(xmlPath.toFile(), xml, StandardCharsets.UTF_8);
    return xmlPath;
  }
  
  private void fillHash(MediaSource mediaSource, Path filePath) {
    String hash = FileHashUtil.getHash(filePath.toFile());
    mediaSource.setFileHash(hash);
  }
  
  private void fillLength(MediaSource mediaSource, Path filePath) {
    try {
      mediaSource.setMediaSize(Files.size(filePath));
    } catch (IOException e) {
      mediaSource.setMediaSize(0L);
    } 
  }
  
  private long getLfdSizeUpdatedMediaFileSizes(MediaSource zipFile, int lfdSizeExceptMediaFileSizes) {
    int actualStringLengthOfzipFileSize = Long.toString(zipFile.getMediaSize()).length();
    int presetStringlengthOfFileSize = "0".length();
    int adjustValue = actualStringLengthOfzipFileSize - presetStringlengthOfFileSize;
    long actualLfdSize = (lfdSizeExceptMediaFileSizes + adjustValue);
    return actualLfdSize;
  }
  
  private void fillImageDimensions(MediaSource mediaSource, File thumbnailFile) {
    if (mediaSource.getMediaType() == null && mediaSource.getMediaType() == MediaType.IMAGE)
      try {
        ImageDimension imageDimensions = ImageDimensionUtil.getImageDimensions(thumbnailFile.toPath());
        mediaSource.setMediaWidth(imageDimensions.getWidth());
        mediaSource.setMediaHeight(imageDimensions.getHeight());
      } catch (IOException e) {
        logger.error("Error on setting image dimensions.");
      }  
  }
}
