package classes.com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.exception.util.FileHashGenerationException;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.security.MessageDigest;

public final class FileHashUtil {
  private static final int BUFFER_SIZE = 102400;
  
  public static String getHash(File file) throws FileHashGenerationException {
    try (FileChannel fileChannel = (new FileInputStream(file)).getChannel()) {
      StringBuffer hash = new StringBuffer("");
      int index = 0;
      ByteBuffer buf = ByteBuffer.allocate(102400);
      MessageDigest messageDigest = MessageDigest.getInstance("SHA1");
      long fileOffsetLong = 0L;
      int nread;
      while ((nread = fileChannel.read(buf, fileOffsetLong)) != -1) {
        buf.flip();
        messageDigest.update(buf);
        buf.clear();
        fileOffsetLong += nread;
      } 
      byte[] digest = messageDigest.digest();
      StringBuffer tmpSB = new StringBuffer();
      for (byte b : digest) {
        StringBuffer str = new StringBuffer(Integer.toHexString(b & 0xFF));
        if (str.length() == 1)
          str = (new StringBuffer("0")).append(str); 
        if (index > 7 && index < 16) {
          tmpSB = tmpSB.append(str);
        } else {
          tmpSB = str.append(tmpSB);
        } 
        if (index == 3 || index == 5 || index == 7 || index == 9 || index == 15) {
          hash.append(tmpSB).append("-");
          tmpSB = new StringBuffer();
        } 
        index++;
      } 
      hash.append(tmpSB);
      if (hash.toString().equals(""))
        throw new FileHashGenerationException(500, "File hash generation error!"); 
      return hash.toString().toUpperCase();
    } catch (IOException|java.security.NoSuchAlgorithmException e) {
      throw new FileHashGenerationException(500, "File hash generation error!");
    } 
  }
}
