package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.google.common.collect.ImmutableSet;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.service.DeviceTypeService;
import java.util.List;
import java.util.Set;
import javax.inject.Inject;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/deviceTypes"})
public class DeviceTypeQueryController {
  private final DeviceTypeService deviceTypeService;
  
  @Inject
  public DeviceTypeQueryController(DeviceTypeService deviceTypeService) {
    this.deviceTypeService = deviceTypeService;
  }
  
  @PostMapping(consumes = {"application/json"})
  public HttpEntity<DeviceType[]> setDeviceType(@RequestBody List<DeviceType> deviceTypes) {
    ImmutableSet immutableSet = ImmutableSet.copyOf(deviceTypes);
    this.deviceTypeService.setSupportedDeviceTypes((Set)immutableSet);
    return (HttpEntity<DeviceType[]>)ResponseEntity.ok(DeviceType.values());
  }
  
  @GetMapping({"/compatiblePlayerType"})
  public HttpEntity<String> getCompatiblePlayerType(@RequestParam(required = true) String playerType) {
    String compatiblePlayerType = DeviceType.getCompatiblePlayerType(playerType);
    return (HttpEntity<String>)ResponseEntity.ok(compatiblePlayerType);
  }
}
