package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.LFDCanvasContent;
import java.util.Set;

public class LFDContent {
  private final Content lfdInfo;
  
  private final String fileHash;
  
  private final long fileSize;
  
  private final Set<LFDCanvasContent> canvasContents;
  
  public LFDContent(Content lfdInfo, String fileHash, long fileSize, Set<LFDCanvasContent> canvasContents) {
    this.lfdInfo = lfdInfo;
    this.fileHash = fileHash;
    this.fileSize = fileSize;
    this.canvasContents = canvasContents;
  }
  
  public Content getLfdInfo() {
    return this.lfdInfo;
  }
  
  public Set<LFDCanvasContent> getCanvasContents() {
    return this.canvasContents;
  }
  
  public String getThumbnailName() {
    return this.lfdInfo.getThumbnailName();
  }
  
  public String getId() {
    return this.lfdInfo.getId();
  }
  
  public String getThumbnailId() {
    return this.lfdInfo.getThumbnailId();
  }
  
  public String getFileId() {
    return this.lfdInfo.getFileId();
  }
  
  public String getFileName() {
    return this.lfdInfo.getFileName();
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public String getFileHash() {
    return this.fileHash;
  }
}
