package classes.com.samsung.magicinfo.webauthor2.repository.model;

import javax.xml.bind.annotation.XmlElement;

public class RestApiHardwareUniqueKeyResponse {
  @XmlElement(name = "apiVersion")
  private String apiVersion;
  
  @XmlElement(name = "status")
  private String status;
  
  @XmlElement(name = "items")
  private String items;
  
  public String getApiVersion() {
    return this.apiVersion;
  }
  
  public String getStatus() {
    return this.status;
  }
  
  public String getItems() {
    return this.items;
  }
}
