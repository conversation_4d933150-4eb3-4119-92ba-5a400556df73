package classes.com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.datalink.SyncGroupType;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PageType", propOrder = {"name", "syncGroups"})
public class PageType {
  @XmlElement(name = "Name", required = true)
  @XmlCDATA
  protected String name;
  
  @XmlElement(name = "SyncGroup")
  protected List<SyncGroupType> syncGroups;
  
  @XmlAttribute(name = "no")
  protected int no;
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String value) {
    this.name = value;
  }
  
  public List<SyncGroupType> getSyncGroups() {
    return this.syncGroups;
  }
  
  public void setSyncGroups(List<SyncGroupType> syncGroups) {
    this.syncGroups = syncGroups;
  }
  
  public int getNo() {
    return this.no;
  }
  
  public void setNo(int value) {
    this.no = value;
  }
}
