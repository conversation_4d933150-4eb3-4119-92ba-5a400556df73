package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.xml.lfd.FileItem;
import java.nio.file.Path;

public class FileItemWrapper {
  Path path;
  
  FileItem fileItem;
  
  String filename;
  
  public FileItemWrapper(FileItem fileItem) {
    this.fileItem = fileItem;
  }
  
  public FileItemWrapper(Path path, FileItem fileItem, String filename) {
    this.path = path;
    this.fileItem = fileItem;
    this.filename = filename;
  }
  
  public Path getPath() {
    return this.path;
  }
  
  public void setPath(Path path) {
    this.path = path;
  }
  
  public FileItem getFileItem() {
    return this.fileItem;
  }
  
  public void setFileItem(FileItem fileItem) {
    this.fileItem = fileItem;
  }
  
  public String getFilename() {
    return this.filename;
  }
  
  public void setFilename(String filename) {
    this.filename = filename;
  }
}
