package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupLayout;
import java.util.List;

public interface OpenAPIDeviceRepository {
  List<DeviceGroupData> getDeviceGroupList();
  
  List<DeviceGroupData> getDeviceGroupInfo(String paramString);
  
  List<DeviceData> getDeviceList(String paramString);
  
  List<DeviceData> getDeviceListWithDeviceType(String paramString);
  
  DeviceGroupLayout getVideowallLayoutPath(String paramString);
  
  List<DeviceData> getWPlayerDeviceList();
}
