package classes.com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.util.SecuritySetting;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import javax.net.ssl.TrustManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class AdaptableTrustManager {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.util.AdaptableTrustManager.class);
  
  public TrustManager[] getTrustManager(String setting, String keyPath, String keyPassword) throws KeyStoreException, NoSuchAlgorithmException, CertificateException, FileNotFoundException, IOException {
    SecuritySetting securitySettingValue = SecuritySetting.DEFAULT;
    if (false == setting.isEmpty())
      try {
        securitySettingValue = SecuritySetting.valueOf(setting);
      } catch (IllegalArgumentException e) {
        logger.error("Wrong SSL security level name: " + setting + " using the default value:" + securitySettingValue);
      }  
    return new TrustManager[] { (TrustManager)new LocalAdaptableTrustManager(securitySettingValue, keyPath, keyPassword) };
  }
  
  public TrustManager[] getTrustManager(SecuritySetting setting) throws CertificateException, FileNotFoundException, IOException {
    return new TrustManager[] { (TrustManager)new LocalAdaptableTrustManager() };
  }
}
