package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseContentData;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetContentInfoOpenApiMethod extends OpenApiMethod<ContentData, ResponseContentData> {
  private final String userId;
  
  private final String token;
  
  private final String contentId;
  
  public GetContentInfoOpenApiMethod(RestTemplate restTemplate, String userId, String token, String contentId) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.contentId = contentId;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getContentInfo";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("contentId", this.contentId);
    return vars;
  }
  
  Class<ResponseContentData> getResponseClass() {
    return ResponseContentData.class;
  }
  
  ContentData convertResponseData(ResponseContentData responseData) {
    return responseData.getResponseClass();
  }
}
