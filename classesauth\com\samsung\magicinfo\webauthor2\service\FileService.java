package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import java.io.FileNotFoundException;
import java.util.List;

public interface FileService {
  List<String> getFileTypeList(MediaType paramMediaType, DeviceType paramDeviceType);
  
  List<String> getAllSupportedFileTypesForDevice(DeviceType paramDeviceType);
  
  FileInfo getFileInfo(String paramString) throws FileNotFoundException;
  
  List<FileInfo> getListOfFileInfos(List<String> paramList);
  
  String generateRandomFileId();
}
