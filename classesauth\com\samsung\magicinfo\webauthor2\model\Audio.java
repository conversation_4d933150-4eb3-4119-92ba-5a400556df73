package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.util.PlayTimeUtil;

public class Audio {
  private Double duration;
  
  public static com.samsung.magicinfo.webauthor2.model.Audio fromData(ContentData contentData) {
    Double playTime = PlayTimeUtil.convertPlayTime(contentData.getPlayTime());
    return new com.samsung.magicinfo.webauthor2.model.Audio(playTime);
  }
  
  public Audio(Double duration) {
    this.duration = duration;
  }
  
  public Audio() {}
  
  public Double getDuration() {
    return this.duration;
  }
}
