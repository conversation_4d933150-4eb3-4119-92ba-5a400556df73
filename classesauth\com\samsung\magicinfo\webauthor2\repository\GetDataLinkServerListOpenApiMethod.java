package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.DataLinkServerListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.DataLinkServerListResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.DatalinkServerEntityData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetDataLinkServerListOpenApiMethod extends OpenApiMethod<List<DatalinkServerEntityData>, DataLinkServerListResponseData> {
  private final String userId;
  
  private final String token;
  
  public GetDataLinkServerListOpenApiMethod(RestTemplate restTemplate, String userId, String token) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "CommonSettingService";
  }
  
  protected String getOpenApiMethodName() {
    return "getDataLinkServerList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    return vars;
  }
  
  Class<DataLinkServerListResponseData> getResponseClass() {
    return DataLinkServerListResponseData.class;
  }
  
  List<DatalinkServerEntityData> convertResponseData(DataLinkServerListResponseData responseData) {
    List<DatalinkServerEntityData> datalinkServerEntityList;
    DataLinkServerListResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      datalinkServerEntityList = resultListData.getResultList();
    } else {
      datalinkServerEntityList = new ArrayList<>();
    } 
    return datalinkServerEntityList;
  }
}
