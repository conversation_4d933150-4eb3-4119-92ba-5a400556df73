package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.io.Serializable;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.web.context.annotation.SessionScope;

@Component
@SessionScope
public class ContentSaveElements implements Serializable {
  private static final long serialVersionUID = 3992197199810192852L;
  
  private String xml;
  
  private String projectName;
  
  private String contentName;
  
  private MediaSource projectThumbnailMediaSource;
  
  private String playTime;
  
  private DeviceType playerType;
  
  private int width;
  
  private int height;
  
  private String version = "1";
  
  private boolean duplicate = false;
  
  private List<MediaSource> mediaSources;
  
  private List<Long> fileSizes;
  
  private String pathToThumbnail;
  
  public ContentSaveElements() {
    this.projectThumbnailMediaSource = new MediaSource();
  }
  
  public ContentSaveElements(String xml, String projectName, String contentName, MediaSource projectThumbnailMediaSource, String playTime, DeviceType playerType, int width, int height, List<MediaSource> mediaSources) {
    this.xml = xml;
    this.projectName = projectName;
    this.contentName = contentName;
    this.projectThumbnailMediaSource = projectThumbnailMediaSource;
    this.playTime = playTime;
    this.playerType = playerType;
    this.width = width;
    this.height = height;
    this.mediaSources = mediaSources;
  }
  
  public String getProjectContentId() {
    return ((MediaSource)this.mediaSources.get(0)).getContentId();
  }
  
  public String getXml() {
    return this.xml;
  }
  
  public void setXml(String xml) {
    this.xml = xml;
  }
  
  public String getProjectName() {
    return this.projectName;
  }
  
  public void setProjectName(String projectName) {
    this.projectName = projectName;
  }
  
  public String getContentName() {
    return this.contentName;
  }
  
  public void setContentName(String contentName) {
    this.contentName = contentName;
  }
  
  public String getPlayTime() {
    return this.playTime;
  }
  
  public void setPlayTime(String playTime) {
    this.playTime = playTime;
  }
  
  public DeviceType getPlayerType() {
    return this.playerType;
  }
  
  public void setPlayerType(DeviceType playerType) {
    this.playerType = playerType;
  }
  
  public int getWidth() {
    return this.width;
  }
  
  public void setWidth(int width) {
    this.width = width;
  }
  
  public int getHeight() {
    return this.height;
  }
  
  public void setHeight(int height) {
    this.height = height;
  }
  
  public List<MediaSource> getMediaSources() {
    return this.mediaSources;
  }
  
  public void setMediaSources(List<MediaSource> mediaSources) {
    this.mediaSources = mediaSources;
  }
  
  public MediaSource getProjectThumbnailMediaSource() {
    return this.projectThumbnailMediaSource;
  }
  
  public void setProjectThumbnailMediaSource(MediaSource projectThumbnailMediaSource) {
    this.projectThumbnailMediaSource = projectThumbnailMediaSource;
  }
  
  public String getVersion() {
    return this.version;
  }
  
  public void setVersion(String version) {
    this.version = version;
  }
  
  public boolean isDuplicate() {
    return this.duplicate;
  }
  
  public void setDuplicate(boolean duplicate) {
    this.duplicate = duplicate;
  }
  
  public List<Long> getFileSizes() {
    return this.fileSizes;
  }
  
  public void setFileSizes(List<Long> fileSizes) {
    this.fileSizes = fileSizes;
  }
  
  public String getPathToThumbnail() {
    return this.pathToThumbnail;
  }
  
  public void setPathTothumbnail(String pathToThumbnail) {
    this.pathToThumbnail = pathToThumbnail;
  }
  
  public String toString() {
    return "ContentSaveElements [xml=" + this.xml + ", projectName=" + this.projectName + ", contentName=" + this.contentName + ", playTime=" + this.playTime + ", playerType=" + this.playerType + ", width=" + this.width + ", height=" + this.height + ", mediaSources=" + this.mediaSources + "]";
  }
}
