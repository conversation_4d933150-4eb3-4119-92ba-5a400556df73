package classes.com.samsung.magicinfo.webauthor2.service.textImage;

import com.samsung.magicinfo.webauthor2.model.svg.Color;
import com.samsung.magicinfo.webauthor2.model.svg.TextImageDescriptor;
import com.samsung.magicinfo.webauthor2.service.textImage.HTMLgenerationService;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

@Service
public class HTMLgenerationServiceImpl implements HTMLgenerationService {
  private static final Logger LOGGER = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.textImage.HTMLgenerationServiceImpl.class);
  
  public void createHTMLfile(String htmlPath, TextImageDescriptor textElement, String fontURL) {
    String htmlString = createHTMLstring(textElement, fontURL);
    try {
      FileUtils.writeStringToFile(new File(htmlPath), htmlString, StandardCharsets.UTF_8);
    } catch (IOException ex) {
      LOGGER.error("HTMLfileWrite", ex.getMessage());
    } 
  }
  
  public String createFontFamilyTextHTMLfile(String htmlPath, TextImageDescriptor textElement, String fontURL) {
    String htmlString = createFontFamilyTextHTMLstring(textElement, fontURL);
    try {
      FileUtils.writeStringToFile(new File(htmlPath), htmlString, StandardCharsets.UTF_8);
    } catch (IOException ex) {
      LOGGER.error("HTMLfileWrite", ex.getMessage());
    } 
    return htmlString;
  }
  
  public void createHTMLfileEmbedFont(String htmlPath, TextImageDescriptor textElement, String base64EncodedFont) {
    String htmlString = createHTMLstring(textElement, embedUrlEncodedFont(base64EncodedFont));
    try {
      FileUtils.writeStringToFile(new File(htmlPath), htmlString, StandardCharsets.UTF_8);
    } catch (IOException ex) {
      LOGGER.error("HTMLfileWrite", ex.getMessage());
    } 
  }
  
  private String embedUrlEncodedFont(String base64EncodedFont) {
    StringBuilder sb = new StringBuilder();
    sb.append("'data:application/x-font-ttf;base64,\"");
    sb.append(base64EncodedFont);
    sb.append("\"");
    return sb.toString();
  }
  
  public String createHTMLstring(TextImageDescriptor textElement, String fontURL) {
    StringBuilder data = new StringBuilder();
    int from = textElement.getOutlineWidth() / 2 * -1;
    int to = textElement.getOutlineWidth() / 2;
    String outline = "";
    String verticalAlignStyle = "";
    String dimensionsStyle = "";
    String styles = "";
    for (int i = from; i < to; i++) {
      for (int j = from; j < to; j++)
        outline = outline + i + "px " + j + "px  0 " + textElement.getOutlineColor() + ","; 
    } 
    outline = outline.substring(0, outline.length() - 1);
    String shadow = textElement.getShadowOffset() + "px " + textElement.getShadowOffset() + "px 0 " + textElement.getShadowColor().toString();
    String bgColor = "transparent";
    if (textElement.getBackgroundColor() != null) {
      Color backgroundColor = textElement.getBackgroundColor();
      Color backgroundColorForHtml = new Color(backgroundColor.getRed(), backgroundColor.getGreen(), backgroundColor.getBlue(), textElement.getBackgroundOpacity() / 100.0D);
      bgColor = backgroundColorForHtml.toString();
    } 
    switch (textElement.getTextVerticalAlign()) {
      case "top":
        verticalAlignStyle = "bottom: auto; top: 0;";
        dimensionsStyle = " width: " + textElement.getWidth() + "px; height: " + textElement.getHeight() + "px;";
        break;
      case "middle":
        verticalAlignStyle = "bottom: auto; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%);";
        break;
      case "bottom":
        verticalAlignStyle = "bottom: 0; top: auto;";
        break;
    } 
    styles = "<style type=\"text/css\">@font-face {font-family:'" + textElement.getFontFamily() + "';src: url('" + fontURL + "') format('truetype');}</style>";
    data.append("<html>");
    data.append("<head>");
    data.append(styles);
    data.append("<meta charset=\"utf-8\">\n");
    data.append("</head>");
    data.append("<body>");
    data.append("<div style = \"position: absolute; top: 0; left: 0; width: ");
    data.append(textElement.getWidth());
    data.append("px; height: ");
    data.append(textElement.getHeight());
    data.append("px;\">");
    data.append("<div ");
    data.append("style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
    data.append(textElement.getWidth());
    data.append("px; height: ");
    data.append(textElement.getHeight());
    data.append("px; background-color: ");
    data.append(bgColor);
    data.append(";\"></div>");
    data.append("<div ");
    data.append(" style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
    data.append(textElement.getWidth());
    data.append("px;");
    data.append(dimensionsStyle);
    data.append("   color:  ");
    data.append(textElement.getColor().toString());
    data.append(" ; font-family: '");
    data.append(textElement.getFontFamily());
    data.append("'; font-size: ");
    data.append(textElement.getFontSize1_24());
    data.append("px; ");
    data.append("   font-weight: ");
    data.append(textElement.getFontWeight());
    data.append(";  ");
    data.append("   font-style: ");
    data.append(textElement.getFontStyle());
    data.append("; ");
    data.append("   text-decoration: ");
    data.append(textElement.getTextDecoration());
    data.append("; ");
    data.append("   word-wrap: break-word; white-space: normal; ");
    data.append("   text-align: ");
    data.append(textElement.getTextAlign());
    data.append(";");
    data.append("   overflow: hidden;");
    data.append(verticalAlignStyle);
    data.append("\">");
    data.append(textElement.getText());
    data.append("</div>");
    if (textElement.isShadow()) {
      data.append("<div ");
      data.append(" style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
      data.append(textElement.getWidth());
      data.append("px;");
      data.append("   color: ");
      data.append(textElement.getColor().toString());
      data.append("; font-family: '");
      data.append(textElement.getFontFamily());
      data.append("' ; font-size: ");
      data.append(textElement.getFontSize1_24());
      data.append("px;");
      data.append(dimensionsStyle);
      data.append("   font-weight: ");
      data.append(textElement.getFontWeight());
      data.append(";    font-style: ");
      data.append(textElement.getFontStyle());
      data.append(";    text-decoration: ");
      data.append(textElement.getTextDecoration());
      data.append(";    word-wrap: break-word; white-space: normal; ");
      data.append("   text-align: ");
      data.append(textElement.getTextAlign());
      data.append(";");
      data.append("   text-shadow: ");
      data.append(shadow);
      data.append(";    overflow: hidden;    opacity: ");
      data.append(textElement.getShadowOpacityDivided());
      data.append(";");
      data.append(verticalAlignStyle);
      data.append("\">");
      data.append(textElement.getText());
      data.append("</div>");
    } 
    if (textElement.isOutline()) {
      data.append("<div");
      data.append(" style=\"bottom: 0; left: 0; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: ");
      data.append(textElement.getWidth());
      data.append("px;");
      data.append("   color: ");
      data.append(textElement.getColor().toString());
      data.append("; font-family: '");
      data.append(textElement.getFontFamily());
      data.append("'; font-size: ");
      data.append(textElement.getFontSize1_24());
      data.append("px;");
      data.append(dimensionsStyle);
      data.append("   font-weight: ");
      data.append(textElement.getFontWeight());
      data.append(";    font-style: ");
      data.append(textElement.getFontStyle());
      data.append(";    text-decoration: ");
      data.append(textElement.getTextDecoration());
      data.append(";    word-wrap: break-word; white-space: normal; ");
      data.append("   text-align: ");
      data.append(textElement.getTextAlign());
      data.append(";");
      data.append("   text-shadow: ");
      data.append(outline);
      data.append(";    overflow: hidden;    opacity: ");
      data.append(textElement.getOutlineOpacityDivided());
      data.append(";");
      data.append(verticalAlignStyle);
      data.append("\">");
      data.append(textElement.getText());
      data.append("</div>");
    } 
    data.append("</div>");
    data.append("</body>");
    data.append("</html>");
    return data.toString();
  }
  
  public String createFontFamilyTextHTMLstring(TextImageDescriptor textElement, String fontURL) {
    StringBuilder data = new StringBuilder();
    String verticalAlignStyle = "";
    String dimensionsStyle = "";
    switch (textElement.getTextVerticalAlign()) {
      case "top":
        verticalAlignStyle = "bottom: auto; top: 0;";
        dimensionsStyle = " width: " + textElement.getWidth() + "px; height: " + textElement.getHeight() + "px;";
        break;
      case "middle":
        verticalAlignStyle = "bottom: auto; top: 50%; transform: translateY(-50%); -webkit-transform: translateY(-50%); -moz-transform: translateY(-50%);";
        break;
      case "bottom":
        verticalAlignStyle = "bottom: 0; top: auto;";
        break;
    } 
    data.append("<div ");
    data.append(" style=\"bottom: 0; left: 14px; padding: 2px;  position: absolute; right: 0; top: 0; white-space: pre-wrap; word-wrap: break-word; width: calc(100% - 14px); height: 100%;");
    data.append(dimensionsStyle);
    data.append("   color:  ");
    data.append(textElement.getColor().toString());
    data.append(" ; font-family: '");
    data.append(textElement.getFontFamily());
    data.append("'; font-size: ");
    data.append(textElement.getFontSize());
    data.append("px; ");
    data.append("   font-weight: ");
    data.append(textElement.getFontWeight());
    data.append(";  ");
    data.append("   font-style: ");
    data.append(textElement.getFontStyle());
    data.append("; ");
    data.append("   text-decoration: ");
    data.append(textElement.getTextDecoration());
    data.append("; ");
    data.append("   word-wrap: break-word; white-space: normal; ");
    data.append("   text-align: ");
    data.append(textElement.getTextAlign());
    data.append(";");
    data.append("   overflow: hidden;");
    data.append(verticalAlignStyle);
    data.append("\">");
    data.append(textElement.getFontFamily());
    data.append("</div>");
    return data.toString();
  }
  
  public File createHtmlFileFromString(String htmlContent, String htmlPath) {
    File htmlAsFile = new File(htmlPath);
    try {
      FileUtils.writeStringToFile(htmlAsFile, htmlContent, "UTF-8");
    } catch (IOException ex) {
      LOGGER.error("HTMLfileWrite", ex.getMessage());
    } 
    return htmlAsFile;
  }
}
