package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import ch.qos.logback.classic.Level;
import com.samsung.magicinfo.webauthor2.exception.service.UnauthorizedAccessException;
import com.samsung.magicinfo.webauthor2.model.LogLevelResponse;
import com.samsung.magicinfo.webauthor2.service.LogService;
import com.samsung.magicinfo.webauthor2.util.LogLevelUtil;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/log"})
public class LogController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.LogController.class);
  
  private LogService logService;
  
  @Autowired
  public LogController(LogService logService) {
    this.logService = logService;
  }
  
  @GetMapping
  public void getLogs(@RequestParam(name = "day", defaultValue = "0") int lastDays, HttpServletResponse response) throws IOException {
    Path logsZip = this.logService.getLogsZip(lastDays);
    writeLogFileToResponse(response, logsZip);
  }
  
  private void writeLogFileToResponse(HttpServletResponse response, Path logsZip) throws IOException {
    response.setContentType("application/zip");
    response.setHeader("Content-disposition", "attachment; filename=log.zip");
    ServletOutputStream servletOutputStream = response.getOutputStream();
    try (InputStream inputStream = Files.newInputStream(logsZip, new java.nio.file.OpenOption[0])) {
      IOUtils.copy(inputStream, (OutputStream)servletOutputStream);
      response.flushBuffer();
    } finally {
      deleteDirectoryWithLogFile(logsZip);
    } 
  }
  
  private void deleteDirectoryWithLogFile(Path logsZip) {
    Path parent = logsZip.getParent();
    if (parent != null && Files.exists(parent, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(parent.toFile()); 
  }
  
  @GetMapping({"/level"})
  public HttpEntity<LogLevelResponse> getLogLevelStatus() throws IOException {
    Level currentLogLevel = LogLevelUtil.getLogLevel();
    return (HttpEntity<LogLevelResponse>)ResponseEntity.ok(new LogLevelResponse(currentLogLevel.toString()));
  }
  
  @PutMapping({"/level"})
  public HttpEntity<LogLevelResponse> putLogLevel(@RequestParam(defaultValue = "default") String level) {
    this.logService.setLogLevel(level);
    Level newLevel = LogLevelUtil.getLogLevel();
    return (HttpEntity<LogLevelResponse>)ResponseEntity.ok(new LogLevelResponse(newLevel.toString()));
  }
  
  @GetMapping({"/setlevel"})
  public HttpEntity<LogLevelResponse> setLogLevel(@RequestParam(name = "level", required = true) String level) {
    this.logService.setLogLevel(level);
    Level newLevel = LogLevelUtil.getLogLevel();
    return (HttpEntity<LogLevelResponse>)ResponseEntity.ok(new LogLevelResponse(newLevel.toString()));
  }
  
  @ExceptionHandler({IOException.class})
  public ResponseEntity<String> ioExceptionHandler(IOException ex) {
    logger.error(ex.getMessage(), ex);
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
  
  @ExceptionHandler({UnauthorizedAccessException.class})
  public ResponseEntity<String> unauthorizedAccessExceptionHandler(UnauthorizedAccessException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(ex.getMessage());
  }
  
  @ExceptionHandler({IllegalArgumentException.class})
  public ResponseEntity<String> illegalArgumentExceptionHandler(IllegalArgumentException ex) {
    logger.error(ex.getMessage(), ex);
    return ResponseEntity.badRequest().body(ex.getMessage());
  }
}
