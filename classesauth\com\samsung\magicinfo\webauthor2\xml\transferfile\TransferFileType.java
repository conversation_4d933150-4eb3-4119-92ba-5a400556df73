package classes.com.samsung.magicinfo.webauthor2.xml.transferfile;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlRootElement(name = "TransferFile")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransferFileType", propOrder = {"fileName", "fileSize", "storePath", "fileId", "fileHashValue"})
public class TransferFileType {
  @XmlElement(name = "FileName")
  @XmlCDATA
  private String fileName;
  
  @XmlElement(name = "FileSize")
  private long fileSize;
  
  @XmlElement(name = "StorePath")
  private String storePath;
  
  @XmlElement(name = "FileID")
  private String fileId;
  
  @XmlElement(name = "FileHashValue")
  private String fileHashValue;
  
  @XmlAttribute
  private String type;
  
  @XmlAttribute(name = "supportfileitems")
  private boolean supportFileItems;
  
  @XmlAttribute
  private int reqIndex;
  
  public void setFileName(String fileName) {
    this.fileName = fileName;
  }
  
  public void setFileSize(long fileSize) {
    this.fileSize = fileSize;
  }
  
  public void setStorePath(String storePath) {
    this.storePath = storePath;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public void setFileHashValue(String fileHashValue) {
    this.fileHashValue = fileHashValue;
  }
  
  public void setType(String type) {
    this.type = type;
  }
  
  public void setSupportFileItems(boolean supportFileItems) {
    this.supportFileItems = supportFileItems;
  }
  
  public void setReqIndex(int reqIndex) {
    this.reqIndex = reqIndex;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public String getStorePath() {
    return this.storePath;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileHashValue() {
    return this.fileHashValue;
  }
  
  public String getType() {
    return this.type;
  }
  
  public boolean isSupportFileItems() {
    return this.supportFileItems;
  }
  
  public int getReqIndex() {
    return this.reqIndex;
  }
}
