package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.FileInfo;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIFileRepository;
import com.samsung.magicinfo.webauthor2.repository.model.FileInfoData;
import com.samsung.magicinfo.webauthor2.service.FileService;
import java.io.FileNotFoundException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class FileServiceImpl implements FileService {
  private OpenAPIFileRepository repository;
  
  @Autowired
  public FileServiceImpl(OpenAPIFileRepository repository) {
    this.repository = repository;
  }
  
  public List<String> getFileTypeList(MediaType mediaType, DeviceType deviceType) {
    return this.repository.getFileTypeList(mediaType, deviceType);
  }
  
  public List<String> getAllSupportedFileTypesForDevice(DeviceType deviceType) {
    List<String> allSupportedFormats = new ArrayList<>();
    for (MediaType mediaType : MediaType.values())
      allSupportedFormats.addAll(getFileTypeList(mediaType, deviceType)); 
    return allSupportedFormats;
  }
  
  public FileInfo getFileInfo(String fileId) throws FileNotFoundException, IllegalArgumentException {
    Assert.notNull(fileId, "File id can't be null!");
    Assert.hasLength(fileId, "File can't be empty!");
    FileInfoData fileInfoData = this.repository.getFileInfo(fileId);
    if (fileInfoData == null)
      throw new FileNotFoundException("No file with id: " + fileId); 
    return getFileInfoFromFileInfoData(fileInfoData);
  }
  
  public List<FileInfo> getListOfFileInfos(List<String> fileIds) {
    if (fileIds == null)
      throw new IllegalArgumentException(); 
    List<FileInfo> list = new ArrayList<>();
    for (String fileId : fileIds) {
      FileInfoData fileInfoData = this.repository.getFileInfo(fileId);
      if (fileInfoData != null) {
        FileInfo fileInfo = getFileInfoFromFileInfoData(fileInfoData);
        list.add(fileInfo);
      } 
    } 
    return list;
  }
  
  private FileInfo getFileInfoFromFileInfoData(FileInfoData fileInfoData) {
    return new FileInfo(fileInfoData.getFileId(), fileInfoData.getFilePath(), fileInfoData.getFileName(), fileInfoData
        .getFileHash(), fileInfoData.getSize());
  }
  
  public String generateRandomFileId() {
    return UUID.randomUUID().toString().toUpperCase();
  }
}
