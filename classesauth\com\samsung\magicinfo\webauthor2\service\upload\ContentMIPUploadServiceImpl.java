package classes.com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.upload.ContentMIPUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.JobStateService;
import com.samsung.magicinfo.webauthor2.service.upload.UploadService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ContentMIPUploadServiceImpl implements ContentMIPUploadService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.upload.ContentMIPUploadServiceImpl.class);
  
  private final UploadService uploadService;
  
  private final JobStateService jobStateService;
  
  private final UserData userData;
  
  private ServletContext servletContext;
  
  public static final String DEFAULT_WORKING_DIRECTORY_ROOT = "insertContents";
  
  @Autowired
  public ContentMIPUploadServiceImpl(UploadService uploadService, JobStateService jobStateService, UserData userData, ServletContext servletContext) {
    this.uploadService = uploadService;
    this.jobStateService = jobStateService;
    this.userData = userData;
    this.servletContext = servletContext;
  }
  
  public String upload(ContentSaveElements contentSaveElements) {
    String projectContentId = contentSaveElements.getProjectContentId();
    String version = contentSaveElements.getVersion();
    String userId = this.userData.getUserId();
    String token = this.userData.getToken();
    List<MediaSource> mediaSources = contentSaveElements.getMediaSources();
    MediaSource projectThumbnailMediaSource = contentSaveElements.getProjectThumbnailMediaSource();
    try {
      this.uploadService.upload(mediaSources, projectContentId);
      this.uploadService.upload(projectThumbnailMediaSource, projectContentId);
      this.jobStateService.jobStateSuccess(userId, token, projectContentId, version, contentSaveElements.isDuplicate());
    } catch (UploaderException ex) {
      logger.error("Project saving with HTTP failed.");
      this.jobStateService.jobStateFail(userId, token, projectContentId, version);
      throw new UploaderException(ex.getErrorCode(), ex.getMessage());
    } finally {
      cleanUpDirectoryStructure(contentSaveElements);
      contentSaveElements.setProjectThumbnailMediaSource(new MediaSource());
    } 
    return projectContentId;
  }
  
  private void deleteSecurely(Path path) {
    if (null == path)
      return; 
    if (false == Files.exists(path, new java.nio.file.LinkOption[0]))
      return; 
    Boolean isInsideWorkingDir = Boolean.valueOf(path.startsWith(this.servletContext.getRealPath("insertContents")));
    if (false == isInsideWorkingDir.booleanValue())
      return; 
    FileUtils.deleteQuietly(path.toFile());
  }
  
  private void cleanUpDirectoryStructure(ContentSaveElements contentSaveElements) {
    Path thumbnailPath = Paths.get(contentSaveElements.getProjectThumbnailMediaSource().getPath(), new String[0]);
    Path thumbnailParent = thumbnailPath.getParent();
    deleteSecurely(thumbnailParent);
    Path mainFilePath = Paths.get(((MediaSource)contentSaveElements.getMediaSources().get(0)).getPath(), new String[0]);
    Path mainFileParent = mainFilePath.getParent();
    deleteSecurely(mainFileParent);
  }
}
