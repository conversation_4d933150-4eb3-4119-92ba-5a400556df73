package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.xml.lfd.SupportFileItem;
import java.nio.file.Path;

public class SupportFileItemWrapper {
  private SupportFileItem supportFileItem;
  
  private Path pathToFile;
  
  private Path relativePath;
  
  public SupportFileItemWrapper(SupportFileItem supportFileItem) {
    this.supportFileItem = supportFileItem;
  }
  
  public SupportFileItemWrapper(SupportFileItem supportFileItem, Path pathToFile, Path relativePath) {
    this.supportFileItem = supportFileItem;
    this.pathToFile = pathToFile;
    this.relativePath = relativePath;
  }
  
  public SupportFileItem getSupportFileItem() {
    return this.supportFileItem;
  }
  
  public void setSupportFileItem(SupportFileItem supportFileItem) {
    this.supportFileItem = supportFileItem;
  }
  
  public Path getPathToFile() {
    return this.pathToFile;
  }
  
  public void setPathToFile(Path pathToFile) {
    this.pathToFile = pathToFile;
  }
  
  public Path getRelativePath() {
    return this.relativePath;
  }
  
  public void setRelativePath(Path relativePath) {
    this.relativePath = relativePath;
  }
  
  public String toString() {
    return "SupportFileItemWrapper{supportFileItem=" + this.supportFileItem
      .toString() + ", pathToFile=" + this.pathToFile + ", relativePath=" + this.relativePath + '}';
  }
}
