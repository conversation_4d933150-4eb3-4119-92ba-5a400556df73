package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;
import com.samsung.magicinfo.webauthor2.service.RemoteContentService;
import java.io.IOException;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.client.UnknownHttpStatusCodeException;

@Controller
public class RemoteContentController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.RemoteContentController.class);
  
  private RemoteContentService remoteContentService;
  
  @Autowired
  public RemoteContentController(RemoteContentService remoteContentService) {
    this.remoteContentService = remoteContentService;
  }
  
  @GetMapping({"/content/{fileId}/{fileName}"})
  public HttpEntity<?> getFile(@PathVariable String fileId, @PathVariable String fileName, @RequestParam(name = "text", defaultValue = "false", required = false) boolean isText) throws IOException {
    String extension = FilenameUtils.getExtension(fileName);
    if (isText || extension.equalsIgnoreCase("LFD") || extension.equalsIgnoreCase("LFT") || extension.equalsIgnoreCase("DLK") || extension
      .equalsIgnoreCase("VWL") || extension.equalsIgnoreCase("TLFD") || extension.equalsIgnoreCase("js") || extension.equalsIgnoreCase("json")) {
      String xml = this.remoteContentService.getXmlFileContents(fileId, fileName);
      return (HttpEntity<?>)ResponseEntity.ok(xml);
    } 
    byte[] content = this.remoteContentService.getContentFileFromMagicInfoServer(fileId, fileName);
    return (HttpEntity<?>)ResponseEntity.ok(content);
  }
  
  @GetMapping({"/vwt/{fileId}/{fileName}"})
  public HttpEntity<String> getVideoWallFile(@PathVariable String fileId, @PathVariable String fileName) throws IOException {
    String vwlFile = this.remoteContentService.getVwlFileFromMagicInfoServer(fileId, fileName);
    return (HttpEntity<String>)ResponseEntity.ok(vwlFile);
  }
  
  @ExceptionHandler({IOException.class})
  public HttpEntity<String> ioExceptionHandler(IOException ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
  
  @ExceptionHandler({UnknownHttpStatusCodeException.class})
  public HttpEntity<String> unknownHttpStatusCodeExceptionExceptionHandler(UnknownHttpStatusCodeException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
  
  @ExceptionHandler({WebAuthorAbstractException.class})
  public HttpEntity<String> webAuthorAbstractExceptionHandler(WebAuthorAbstractException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
}
