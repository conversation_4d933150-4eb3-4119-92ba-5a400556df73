package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.VWLContent;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface VWLContentService {
  VWLContent getVWLContent(String paramString);
  
  Page<Content> getVWLContentList(Pageable paramPageable, DeviceType paramDeviceType);
}
