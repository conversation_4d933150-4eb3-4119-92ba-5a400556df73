package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.service.LFDImportService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.util.ZipFileUtil;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.ListIterator;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class LFDImportServiceImpl implements LFDImportService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.LFDImportServiceImpl.class);
  
  private static final String ROOT_FOLDER = "import";
  
  private static final List<String> VALID_EXTENSIONS = Arrays.asList(new String[] { "lft", "lfd" });
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  private ZipFileUtil zipFileUtil;
  
  @Autowired
  public LFDImportServiceImpl(ServletContext servletContext, UserData userData, ZipFileUtil zipFileUtil) {
    this.servletContext = servletContext;
    this.userData = userData;
    this.zipFileUtil = zipFileUtil;
  }
  
  public List<String> getZipFileListForImport(MultipartFile fileItem) throws IOException {
    String userDirectory = this.userData.getWorkspaceFolderName();
    Path importDirectory = Paths.get(this.servletContext.getRealPath("import"), new String[0]);
    Path workingDirectory = Paths.get(this.servletContext.getRealPath("import"), new String[] { userDirectory });
    List<String> extractedFileRelativePath = new ArrayList<>();
    Boolean hasUniqueLfd = Boolean.valueOf(true);
    try {
      if (Files.exists(importDirectory, new java.nio.file.LinkOption[0]))
        FileUtils.deleteQuietly(importDirectory.toFile()); 
      Files.createDirectories(workingDirectory, (FileAttribute<?>[])new FileAttribute[0]);
      Path zipFilePath = Paths.get(workingDirectory.toString(), new String[] { fileItem.getOriginalFilename() });
      FileUtils.copyInputStreamToFile(fileItem.getInputStream(), zipFilePath.toFile());
      List<String> zipFilePaths = this.zipFileUtil.getFilePath(zipFilePath);
      hasUniqueLfd = hasUniqueLfdFileInZip(zipFilePaths);
      if (!hasUniqueLfd.booleanValue())
        throw new UploaderException("Lfd is not exist or multiple"); 
      this.zipFileUtil.extract(zipFilePath, Paths.get(workingDirectory.toString(), new String[0]));
      for (String path : zipFilePaths) {
        extractedFileRelativePath.add(Paths.get("import", new String[] { userDirectory, path }).toString().replace("\\", "/"));
      } 
      return extractedFileRelativePath;
    } catch (IOException e) {
      logger.error(e.getMessage(), e);
      throw new UploaderException(e.getMessage());
    } 
  }
  
  private Boolean hasUniqueLfdFileInZip(List<String> filePaths) {
    ListIterator<String> iter = filePaths.listIterator();
    int lfdCount = 0;
    while (iter.hasNext()) {
      String filePath = iter.next();
      String extension = FilenameUtils.getExtension(filePath).toLowerCase();
      if (VALID_EXTENSIONS.contains(extension))
        lfdCount++; 
    } 
    if (lfdCount == 1)
      return Boolean.valueOf(true); 
    return Boolean.valueOf(false);
  }
}
