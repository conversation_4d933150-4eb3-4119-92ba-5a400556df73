package classes.com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.Value;
import com.samsung.magicinfo.webauthor2.model.datalink.ValueType;

public class FileValue extends Value {
  private final String name;
  
  private final String id;
  
  private final long fileSize;
  
  @JsonCreator
  public FileValue(@JsonProperty("name") String name, @JsonProperty("id") String id, @JsonProperty("fileSize") long fileSize) {
    super(ValueType.FILE);
    this.name = name;
    this.id = id;
    this.fileSize = fileSize;
  }
  
  public String getName() {
    return this.name;
  }
  
  public String getId() {
    return this.id;
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
}
