package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.service.DeviceTypeService;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.Set;
import javax.inject.Inject;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
public class DeviceTypeServiceImpl implements DeviceTypeService {
  private final UserData userData;
  
  @Inject
  public DeviceTypeServiceImpl(UserData userData) {
    this.userData = userData;
  }
  
  public void setSupportedDeviceTypes(Set<DeviceType> deviceTypes) {
    Assert.notNull(deviceTypes, "deviceTypes cannot be null.");
    this.userData.setSupportedDeviceTypes(deviceTypes);
  }
}
