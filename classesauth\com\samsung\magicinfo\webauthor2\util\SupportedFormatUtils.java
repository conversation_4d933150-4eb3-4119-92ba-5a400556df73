package classes.com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.model.MediaType;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

public final class SupportedFormatUtils {
  private static final Map<MediaType, Set<String>> supportedFormats = new HashMap<>();
  
  private static final List<String> OTHER_SUPPORTED_TYPES = Collections.unmodifiableList(Arrays.asList(new String[] { "SFI", "SWF" }));
  
  public static synchronized void setFileFormats(MediaType type, List<String> formats) {
    Set<String> fileFormats = new HashSet<>();
    fileFormats.addAll(formats);
    supportedFormats.put(type, fileFormats);
  }
  
  public static boolean isSupportedFormat(MediaType type, String fileFormat) {
    if (supportedFormats.containsKey(type)) {
      Set<String> formats = supportedFormats.get(type);
      return formats.contains(fileFormat);
    } 
    return false;
  }
  
  public static synchronized boolean isInitialized(MediaType type) {
    return supportedFormats.containsKey(type);
  }
  
  public static boolean isSupportedFormat(String fileFormat) {
    String extension = fileFormat.toUpperCase();
    if (OTHER_SUPPORTED_TYPES.contains(extension))
      return true; 
    Collection<Set<String>> formatsCollection = supportedFormats.values();
    for (Set<String> formats : formatsCollection) {
      if (formats.contains(extension))
        return true; 
    } 
    return false;
  }
  
  public static Map<MediaType, Set<String>> getSupportedformats() {
    return supportedFormats;
  }
  
  public static MediaType getMediaTypeForExtension(String extension) {
    for (Map.Entry<MediaType, Set<String>> e : supportedFormats.entrySet()) {
      MediaType mediaType = e.getKey();
      if (((Set)e.getValue()).contains(extension.toUpperCase()))
        return mediaType; 
    } 
    return null;
  }
}
