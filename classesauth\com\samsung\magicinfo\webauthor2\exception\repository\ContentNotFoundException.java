package classes.com.samsung.magicinfo.webauthor2.exception.repository;

public class ContentNotFoundException extends RuntimeException {
  private static final long serialVersionUID = -7523334566134939002L;
  
  private String code;
  
  private String message;
  
  public ContentNotFoundException(String code, String message) {
    this.code = code;
    this.message = message;
  }
  
  public ContentNotFoundException(String contentId) {
    super("Cannot find content by id == " + contentId);
  }
  
  public String getCode() {
    return this.code;
  }
  
  public String getMessage() {
    return this.message;
  }
}
