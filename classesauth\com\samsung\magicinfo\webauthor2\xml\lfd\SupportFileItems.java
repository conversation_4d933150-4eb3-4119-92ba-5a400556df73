package classes.com.samsung.magicinfo.webauthor2.xml.lfd;

import com.samsung.magicinfo.webauthor2.xml.lfd.SupportFileItem;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "SupportFileItems")
@XmlAccessorType(XmlAccessType.NONE)
public class SupportFileItems {
  @XmlElement(name = "FileItem")
  private List<SupportFileItem> fileItems;
  
  public List<SupportFileItem> getFileItems() {
    return this.fileItems;
  }
  
  public void setFileItems(List<SupportFileItem> fileItems) {
    this.fileItems = fileItems;
  }
}
