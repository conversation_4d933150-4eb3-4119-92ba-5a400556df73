package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableColumnData;
import java.util.ArrayList;
import java.util.List;

public class DataLinkTableColumn {
  private final String name;
  
  private final String value;
  
  public DataLinkTableColumn(String name, String value) {
    this.name = name;
    this.value = value;
  }
  
  public String getValue() {
    return this.value;
  }
  
  public String getName() {
    return this.name;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.DataLinkTableColumn fromData(DLKTableColumnData data) {
    return new com.samsung.magicinfo.webauthor2.model.DataLinkTableColumn(data.getKey(), data.getValue());
  }
  
  public static List<com.samsung.magicinfo.webauthor2.model.DataLinkTableColumn> fromData(List<DLKTableColumnData> dataList) {
    List<com.samsung.magicinfo.webauthor2.model.DataLinkTableColumn> columns = new ArrayList<>();
    for (DLKTableColumnData data : dataList)
      columns.add(fromData(data)); 
    return columns;
  }
}
