package classes.com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.util.LogLevelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;

public class ResetLoggingLevel {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.util.ResetLoggingLevel.class);
  
  private static final String EVERYDAY_AT_MIDNIGHT = "0 0 0 * * *";
  
  @Scheduled(cron = "0 0 0 * * *")
  public void resetLoggingLevels() {
    LogLevelUtil.setDefaultLogLevel();
    logger.info("Log levels reset to defaults.");
  }
}
