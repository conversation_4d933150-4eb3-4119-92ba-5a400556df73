package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.model.ConvertTable;

public class ConvertTableUpdateWrapper {
  private ConvertTable newTable;
  
  private ConvertTable oldTable;
  
  public ConvertTable getNewTable() {
    return this.newTable;
  }
  
  public void setNewTable(ConvertTable newTable) {
    this.newTable = newTable;
  }
  
  public ConvertTable getOldTable() {
    return this.oldTable;
  }
  
  public void setOldTable(ConvertTable oldTable) {
    this.oldTable = oldTable;
  }
}
