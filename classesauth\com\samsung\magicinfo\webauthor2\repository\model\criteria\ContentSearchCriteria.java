package classes.com.samsung.magicinfo.webauthor2.repository.model.criteria;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "ContentSearch")
public class ContentSearchCriteria {
  @XmlElement
  private int pageSize;
  
  @XmlElement
  private String searchType;
  
  @XmlElement
  private int startPos;
  
  @XmlElement
  private String searchCreatorId;
  
  @XmlElement
  private String searchMediaType;
  
  @XmlElement
  private String searchText;
  
  @XmlElement
  private String expirationStatusFilter;
  
  public ContentSearchCriteria() {}
  
  protected ContentSearchCriteria(int pageSize, String searchType, int startPos, String searchCreatorId, String searchMediaType, String searchText, String expirationStatusFilter) {
    this.pageSize = pageSize;
    this.searchType = searchType;
    this.startPos = startPos;
    this.searchCreatorId = searchCreatorId;
    this.searchMediaType = searchMediaType;
    this.searchText = searchText;
    this.expirationStatusFilter = expirationStatusFilter;
  }
  
  public int getPageSize() {
    return this.pageSize;
  }
  
  public String getSearchType() {
    return this.searchType;
  }
  
  public int getStartPos() {
    return this.startPos;
  }
  
  public String getSearchCreatorId() {
    return this.searchCreatorId;
  }
  
  public String getSearchMediaType() {
    return this.searchMediaType;
  }
  
  public String getSearchText() {
    return this.searchText;
  }
  
  public String getExpirationStatusFilter() {
    return this.expirationStatusFilter;
  }
}
