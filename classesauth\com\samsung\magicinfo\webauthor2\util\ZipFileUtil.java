package classes.com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.util.FileNameValidator;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ZipFileUtil {
  private FileNameValidator fileNameValidator;
  
  private MagicInfoProperties magicInfoProperties;
  
  private static final int BUFFER_SIZE = 4096;
  
  @Autowired
  public ZipFileUtil(FileNameValidator fileNameValidator, MagicInfoProperties magicInfoProperties) {
    this.fileNameValidator = fileNameValidator;
    this.magicInfoProperties = magicInfoProperties;
  }
  
  public void extract(Path source, Path destination) throws IOException {
    int zipEentryCount = 0;
    long totalSize = 0L;
    long MAX_ZIP_SIZE = this.magicInfoProperties.getMaxSizeOfZip();
    int MAX_ZIP_FILES_QUANTITIY = this.magicInfoProperties.getMaxQuantitiyOfZipFiles();
    if (!Files.exists(destination, new java.nio.file.LinkOption[0]))
      Files.createDirectories(destination, (FileAttribute<?>[])new FileAttribute[0]); 
    FileInputStream inputStream = null;
    ZipInputStream zipInputStream = null;
    try {
      inputStream = new FileInputStream(source.toFile());
      zipInputStream = new ZipInputStream(inputStream);
      ZipEntry zipEntry;
      while ((zipEntry = zipInputStream.getNextEntry()) != null) {
        if (!isWriteZipEntry(zipEntry, new File(destination.toString())))
          continue; 
        Path entryPath = destination.resolve(zipEntry.getName());
        validateFilename(zipEntry.getName(), ".");
        if (zipEntry.isDirectory()) {
          Files.createDirectories(entryPath, (FileAttribute<?>[])new FileAttribute[0]);
        } else {
          if (!Files.exists(entryPath.getParent(), new java.nio.file.LinkOption[0]))
            Files.createDirectories(entryPath.getParent(), (FileAttribute<?>[])new FileAttribute[0]); 
          if (!isInvalidFileName(entryPath)) {
            totalSize = extractFile(zipInputStream, entryPath, totalSize);
            zipEentryCount++;
            if (zipEentryCount > MAX_ZIP_FILES_QUANTITIY)
              throw new IllegalStateException("Too many files to unzip."); 
            if (totalSize + 4096L > MAX_ZIP_SIZE)
              throw new IllegalStateException("File being unzipped is too big."); 
          } 
        } 
        zipInputStream.closeEntry();
      } 
    } catch (IOException e) {
      throw new IOException("Error during extracting individual File" + e.getMessage());
    } finally {
      if (null != inputStream)
        inputStream.close(); 
      if (null != zipInputStream)
        zipInputStream.close(); 
    } 
  }
  
  private long extractFile(ZipInputStream zipInputStream, Path entryPath, long totalSize) throws IOException {
    File newFile = entryPath.toFile();
    FileOutputStream fos = new FileOutputStream(newFile);
    long MAX_ZIP_SIZE = this.magicInfoProperties.getMaxSizeOfZip();
    byte[] buffer = new byte[4096];
    int read = 0;
    try {
      while (totalSize + 4096L <= MAX_ZIP_SIZE && (read = zipInputStream.read(buffer)) > 0) {
        fos.write(buffer, 0, read);
        totalSize += read;
      } 
      fos.flush();
    } catch (IOException e) {
      throw new IOException("Error during extracting zip File" + e.getMessage());
    } finally {
      fos.close();
    } 
    newFile.setExecutable(false);
    newFile.setWritable(false);
    return totalSize;
  }
  
  public List<String> getFilePath(Path source) throws IOException {
    List<String> result = new ArrayList<>();
    FileInputStream inputStream = null;
    ZipInputStream zipInputStream = null;
    try {
      inputStream = new FileInputStream(source.toFile());
      zipInputStream = new ZipInputStream(inputStream);
      ZipEntry zipEntry = zipInputStream.getNextEntry();
      while (zipEntry != null) {
        result.add(zipEntry.getName());
        zipInputStream.closeEntry();
        zipEntry = zipInputStream.getNextEntry();
      } 
      return result;
    } catch (IOException e) {
      throw new IOException("Error during extracting individual File" + e.getMessage());
    } finally {
      if (null != inputStream)
        inputStream.close(); 
      if (null != zipInputStream)
        zipInputStream.close(); 
    } 
  }
  
  private void validateFilename(String filename, String intendedDir) throws IOException {
    File f = new File(filename);
    String canonicalPath = f.getCanonicalPath();
    File iD = new File(intendedDir);
    String canonicalID = iD.getCanonicalPath();
    if (!canonicalPath.startsWith(canonicalID))
      throw new IllegalStateException("File is outside extraction target directory."); 
  }
  
  private boolean isWriteZipEntry(ZipEntry entry, File destinationDir) {
    File file = new File(destinationDir, entry.getName());
    if (!file.toPath().normalize().startsWith(destinationDir.toPath()))
      return false; 
    return true;
  }
  
  private boolean isInvalidFileName(Path entryPath) {
    Path fileName = entryPath.getFileName();
    if (fileName == null)
      return true; 
    return this.fileNameValidator.filenameHasExecutableType(fileName.toString());
  }
}
