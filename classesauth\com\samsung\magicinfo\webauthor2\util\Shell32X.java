package classes.com.samsung.magicinfo.webauthor2.util;

import com.sun.jna.Native;
import com.sun.jna.platform.win32.Shell32;
import com.sun.jna.win32.W32APIOptions;

public interface Shell32X extends Shell32 {
  public static final com.samsung.magicinfo.webauthor2.util.Shell32X INSTANCE = (com.samsung.magicinfo.webauthor2.util.Shell32X)Native.loadLibrary("shell32", com.samsung.magicinfo.webauthor2.util.Shell32X.class, W32APIOptions.UNICODE_OPTIONS);
  
  public static final int SW_HIDE = 0;
  
  public static final int SW_MAXIMIZE = 3;
  
  public static final int SW_MINIMIZE = 6;
  
  public static final int SW_RESTORE = 9;
  
  public static final int SW_SHOW = 5;
  
  public static final int SW_SHOWDEFAULT = 10;
  
  public static final int SW_SHOWMAXIMIZED = 3;
  
  public static final int SW_SHOWMINIMIZED = 2;
  
  public static final int SW_SHOWMINNOACTIVE = 7;
  
  public static final int SW_SHOWNA = 8;
  
  public static final int SW_SHOWNOACTIVATE = 4;
  
  public static final int SW_SHOWNORMAL = 1;
  
  public static final int SE_ERR_FNF = 2;
  
  public static final int SE_ERR_PNF = 3;
  
  public static final int SE_ERR_ACCESSDENIED = 5;
  
  public static final int SE_ERR_OOM = 8;
  
  public static final int SE_ERR_DLLNOTFOUND = 32;
  
  public static final int SE_ERR_SHARE = 26;
  
  public static final int SEE_MASK_NOCLOSEPROCESS = 64;
  
  int ShellExecute(int paramInt1, String paramString1, String paramString2, String paramString3, String paramString4, int paramInt2);
  
  boolean ShellExecuteEx(SHELLEXECUTEINFO paramSHELLEXECUTEINFO);
}
