package classes.com.samsung.magicinfo.webauthor2.exception.repository;

public class TagNotFoundException extends RuntimeException {
  private static final long serialVersionUID = 886442875371984960L;
  
  private String code;
  
  private String message;
  
  public TagNotFoundException(String code, String message) {
    this.code = code;
    this.message = message;
  }
  
  public String getCode() {
    return this.code;
  }
  
  public String getMessage() {
    return this.message;
  }
}
