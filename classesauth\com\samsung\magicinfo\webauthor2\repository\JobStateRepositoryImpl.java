package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.JobState;
import com.samsung.magicinfo.webauthor2.repository.JobStateRepository;
import com.samsung.magicinfo.webauthor2.repository.model.JobStateResponse;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.net.URI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

@Repository
public class JobStateRepositoryImpl implements JobStateRepository {
  private RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Autowired
  public JobStateRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public JobStateResponse jobStateSuccess(String userId, String token, String contentId, String versionId, boolean isDuplicate) throws UploaderException {
    return jobState(userId, token, contentId, Boolean.valueOf(true), versionId, isDuplicate);
  }
  
  public JobStateResponse jobStateFail(String userId, String token, String contentId, String versionId) throws UploaderException {
    return jobState(userId, token, contentId, Boolean.valueOf(false), versionId, false);
  }
  
  private JobStateResponse jobState(String userId, String token, String contentId, Boolean flag, String versionId, boolean isDuplicate) throws UploaderException {
    try {
      Boolean isUrlAuthNotAllowed = this.userData.isUrlAuthNotAllowedToThisMisServletSession();
      UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/servlet/JobState");
      if (false == isUrlAuthNotAllowed.booleanValue())
        uriComponentsBuilder.queryParam("id", new Object[] { userId }).queryParam("passwd", new Object[] { token }); 
      URI uri = uriComponentsBuilder.build().encode().toUri();
      HttpHeaders headers = new HttpHeaders();
      headers.add("userId", userId);
      headers.add("token", token);
      headers.setContentType(MediaType.MULTIPART_FORM_DATA);
      headers.add("CID", contentId);
      headers.add("VERSION_ID", versionId);
      JobState state = JobState.FAIL;
      if (flag.booleanValue())
        state = isDuplicate ? JobState.EXIST : JobState.COMPLETE; 
      headers.add("JOB_STATE", state.toString());
      headers.add("PROMThumbnailType", "");
      HttpEntity<LinkedMultiValueMap<String, String>> request = new HttpEntity((MultiValueMap)headers);
      ResponseEntity<String> response = this.restTemplate.exchange(uri, HttpMethod.POST, request, String.class);
      String redirectUrl = "";
      HttpStatus statusCode = response.getStatusCode();
      if (statusCode == HttpStatus.OK)
        redirectUrl = response.getHeaders().get("REDIRECT_URL").get(0); 
      return new JobStateResponse(statusCode, redirectUrl);
    } catch (UnknownHttpStatusCodeException ex) {
      int rawStatusCode = ex.getRawStatusCode();
      if (rawStatusCode == 606)
        throw new UploaderException(606, "File doesn't exist on server."); 
      throw new UploaderException(rawStatusCode, "ServerInternalUploadError" + rawStatusCode);
    } 
  }
}
