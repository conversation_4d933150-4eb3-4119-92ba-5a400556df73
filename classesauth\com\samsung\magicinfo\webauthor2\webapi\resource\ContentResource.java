package classes.com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.Content;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class ContentResource extends Resource<Content> implements Serializable {
  private static final long serialVersionUID = 1L;
  
  public ContentResource(Content content, Iterable<Link> links) {
    super(content, links);
  }
  
  public ContentResource(Content content, Link... links) {
    super(content, links);
  }
}
