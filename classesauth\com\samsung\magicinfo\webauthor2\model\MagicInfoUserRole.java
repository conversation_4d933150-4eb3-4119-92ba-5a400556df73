package classes.com.samsung.magicinfo.webauthor2.model;

import java.io.Serializable;

public enum MagicInfoUserRole implements Serializable {
  SERVER_ADMINISTRATOR("Server Administrator"),
  ADMINISTRATOR("Administrator"),
  CONTENT_MANAGER("Content Manager"),
  CONTENT_SCHEDULE_MANAGER("Content Schedule Manager"),
  CONTENT_UPLOADER("Content Uploader"),
  DEVICE_MANAGER("Device Manager"),
  SCHEDULE_EDITOR("Schedule Editor"),
  SCHEDULE_MANAGER("Schedule Manager"),
  USER_MANAGER("User Editor");
  
  private final String fieldDescription;
  
  MagicInfoUserRole(String value) {
    this.fieldDescription = value;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.MagicInfoUserRole getRoleFromString(String role) {
    for (com.samsung.magicinfo.webauthor2.model.MagicInfoUserRole misRole : values()) {
      if (role.equalsIgnoreCase(misRole.toString()))
        return misRole; 
    } 
    throw new IllegalArgumentException();
  }
  
  public String toString() {
    return this.fieldDescription;
  }
}
