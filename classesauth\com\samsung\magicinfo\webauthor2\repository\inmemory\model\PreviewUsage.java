package classes.com.samsung.magicinfo.webauthor2.repository.inmemory.model;

import org.joda.time.LocalDate;

public class PreviewUsage {
  private String contentId;
  
  private int versionId;
  
  private LocalDate lastused;
  
  private String userId;
  
  private String startPage;
  
  private int progress;
  
  public PreviewUsage(String contentId, int versionId, LocalDate lastused, String userId, String startPage, int progress) {
    this.contentId = contentId;
    this.versionId = versionId;
    this.lastused = lastused;
    this.userId = userId;
    this.startPage = startPage;
    this.progress = progress;
  }
  
  public PreviewUsage() {}
  
  public String getContentId() {
    return this.contentId;
  }
  
  public void setContentId(String contentId) {
    this.contentId = contentId;
  }
  
  public int getVersionId() {
    return this.versionId;
  }
  
  public void setVersionId(int versionId) {
    this.versionId = versionId;
  }
  
  public LocalDate getLastused() {
    return this.lastused;
  }
  
  public void setLastused(LocalDate lastused) {
    this.lastused = lastused;
  }
  
  public String getUserId() {
    return this.userId;
  }
  
  public void setUserId(String userId) {
    this.userId = userId;
  }
  
  public String getStartPage() {
    return this.startPage;
  }
  
  public void setStartPage(String startPage) {
    this.startPage = startPage;
  }
  
  public int getProgress() {
    return this.progress;
  }
  
  public void setProgress(int progress) {
    this.progress = progress;
  }
  
  public String toString() {
    return "PreviewUsage{contentId='" + this.contentId + '\'' + ", versionId=" + this.versionId + ", lastused=" + this.lastused + ", userId='" + this.userId + '\'' + ", startPage='" + this.startPage + '\'' + ", progress=" + this.progress + '}';
  }
}
