package classes.com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;

public interface JobStateService {
  void jobStateSuccess(String paramString1, String paramString2, String paramString3, String paramString4, boolean paramBoolean) throws UploaderException;
  
  void jobStateFail(String paramString1, String paramString2, String paramString3, String paramString4) throws UploaderException;
}
