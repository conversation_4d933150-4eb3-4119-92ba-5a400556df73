package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetDLKMappingListOpenApiMethod extends OpenApiMethod<List<ContentData>, ResponseData> {
  private final String userId;
  
  private final String token;
  
  private final String lftContentId;
  
  public GetDLKMappingListOpenApiMethod(RestTemplate restTemplate, String userId, String token, String lftContentId) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.lftContentId = lftContentId;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getDLKmappingList ";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("lft_content_id", this.lftContentId);
    return vars;
  }
  
  Class<ResponseData> getResponseClass() {
    return ResponseData.class;
  }
  
  List<ContentData> convertResponseData(ResponseData responseData) {
    List<ContentData> contentDatas;
    ResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      contentDatas = resultListData.getResultList();
    } else {
      contentDatas = new ArrayList<>();
    } 
    return contentDatas;
  }
}
