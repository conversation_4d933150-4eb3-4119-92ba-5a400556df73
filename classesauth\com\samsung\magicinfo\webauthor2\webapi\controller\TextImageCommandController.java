package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.svg.TextImageDescriptor;
import com.samsung.magicinfo.webauthor2.service.textImage.TextImageService;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.NoSuchFileException;
import java.nio.file.Path;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/textImage"})
public class TextImageCommandController {
  private TextImageService service;
  
  @Autowired
  public TextImageCommandController(TextImageService transcodeService) {
    this.service = transcodeService;
  }
  
  @PostMapping
  public HttpEntity<List<String>> textImage(@RequestBody List<TextImageDescriptor> descriptors) throws IOException {
    List<String> filenames = this.service.transcode(descriptors);
    return (HttpEntity<List<String>>)ResponseEntity.ok(filenames);
  }
  
  @PostMapping({"/fontHtml"})
  public HttpEntity<String> fontHtml(@RequestBody TextImageDescriptor descriptor) throws IOException {
    String textHtml = this.service.transcodeFontHtml(descriptor);
    return (HttpEntity<String>)ResponseEntity.ok(textHtml);
  }
  
  @PostMapping({"/fontImage"})
  public HttpEntity<String> fontImage(@RequestBody TextImageDescriptor descriptor) throws IOException {
    String filename = this.service.transcodeFontImage(descriptor);
    return (HttpEntity<String>)ResponseEntity.ok(filename);
  }
  
  @GetMapping({"/{name}"})
  public HttpEntity<InputStreamResource> getPNG(@PathVariable String name) throws IOException {
    Path pngPath = this.service.getPngFilePath(name);
    return (HttpEntity<InputStreamResource>)ResponseEntity.ok()
      .contentLength(Files.size(pngPath))
      .contentType(MediaType.IMAGE_PNG)
      .body(new InputStreamResource(Files.newInputStream(this.service.getPngFilePath(name), new java.nio.file.OpenOption[0])));
  }
  
  @ResponseStatus(HttpStatus.NOT_FOUND)
  @ExceptionHandler({NoSuchFileException.class})
  public void fileNotFound() {}
}
