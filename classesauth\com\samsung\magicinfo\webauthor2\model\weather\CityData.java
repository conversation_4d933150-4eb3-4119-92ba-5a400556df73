package classes.com.samsung.magicinfo.webauthor2.model.weather;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "City")
@XmlAccessorType(XmlAccessType.FIELD)
public class CityData {
  int index;
  
  @XmlAttribute(name = "Name")
  String name;
  
  @XmlAttribute(name = "Code")
  String code;
  
  public CityData() {}
  
  public CityData(int index, String name, String code) {
    this.index = index;
    this.name = name;
    this.code = code;
  }
  
  public int getIndex() {
    return this.index;
  }
  
  public void setIndex(int index) {
    this.index = index;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public String getCode() {
    return this.code;
  }
  
  public void setCode(String code) {
    this.code = code;
  }
}
