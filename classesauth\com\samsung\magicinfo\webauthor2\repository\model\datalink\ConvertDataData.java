package classes.com.samsung.magicinfo.webauthor2.repository.model.datalink;

import com.samsung.magicinfo.webauthor2.model.ConvertType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlAccessorType(XmlAccessType.FIELD)
public class ConvertDataData {
  @XmlElement(name = "convert_data_name")
  @XmlCDATA
  private String name;
  
  @XmlElement(name = "convert_type")
  private ConvertType convertType;
  
  @XmlElement(name = "from_data")
  @XmlCDATA
  private String from;
  
  @XmlElement(name = "to_data")
  @XmlCDATA
  private String to;
  
  @XmlElement(name = "create_date")
  private String createdDate;
  
  public ConvertDataData() {}
  
  public ConvertDataData(String name, ConvertType convertType, String from, String to, String createdDate) {
    this.name = name;
    this.convertType = convertType;
    this.from = from;
    this.to = to;
    this.createdDate = createdDate;
  }
  
  public String getName() {
    return this.name;
  }
  
  public void setName(String name) {
    this.name = name;
  }
  
  public ConvertType getConvertType() {
    return this.convertType;
  }
  
  public void setConvertType(ConvertType convertType) {
    this.convertType = convertType;
  }
  
  public String getFrom() {
    return this.from;
  }
  
  public void setFrom(String from) {
    this.from = from;
  }
  
  public String getTo() {
    return this.to;
  }
  
  public void setTo(String to) {
    this.to = to;
  }
  
  public String getCreatedDate() {
    return this.createdDate;
  }
  
  public void setCreatedDate(String createdDate) {
    this.createdDate = createdDate;
  }
}
