package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.VWLCanvasContent;
import java.util.Set;

public class VWLContent {
  private final Content vwlInfo;
  
  private final String fileHash;
  
  private final long fileSize;
  
  private final Set<VWLCanvasContent> canvasContents;
  
  public VWLContent(Content vwlInfo, String fileHash, long fileSize, Set<VWLCanvasContent> canvasContents) {
    this.vwlInfo = vwlInfo;
    this.fileHash = fileHash;
    this.fileSize = fileSize;
    this.canvasContents = canvasContents;
  }
  
  public Content getVwlInfo() {
    return this.vwlInfo;
  }
  
  public Set<VWLCanvasContent> getCanvasContents() {
    return this.canvasContents;
  }
  
  public String getThumbnailName() {
    return this.vwlInfo.getThumbnailName();
  }
  
  public String getId() {
    return this.vwlInfo.getId();
  }
  
  public String getThumbnailId() {
    return this.vwlInfo.getThumbnailId();
  }
  
  public String getFileId() {
    return this.vwlInfo.getFileId();
  }
  
  public String getFileName() {
    return this.vwlInfo.getFileName();
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public String getFileHash() {
    return this.fileHash;
  }
}
