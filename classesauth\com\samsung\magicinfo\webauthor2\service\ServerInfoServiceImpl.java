package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.repository.model.OpenApiVersionResponseData;
import com.samsung.magicinfo.webauthor2.service.ServerInfoService;
import java.net.URI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Service
public class ServerInfoServiceImpl implements ServerInfoService {
  private RestTemplate restTemplate;
  
  @Autowired
  public ServerInfoServiceImpl(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
  }
  
  public String getOpenApiVersion() {
    URI uri = UriComponentsBuilder.newInstance().path("/openapi/auth").queryParam("cmd", new Object[] { "isMagicInfo" }).build().toUri();
    OpenApiVersionResponseData response = (OpenApiVersionResponseData)this.restTemplate.getForObject(uri, OpenApiVersionResponseData.class);
    return response.getOpenApiVersion();
  }
}
