package classes.com.samsung.magicinfo.webauthor2.repository.model.device;

import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupListResultListData;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
@XmlAccessorType(XmlAccessType.FIELD)
public class DeviceGroupListResponseData implements Serializable {
  private static final long serialVersionUID = 683857457291657240L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private DeviceGroupListResultListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public DeviceGroupListResultListData getResponseClass() {
    return this.responseClass;
  }
}
