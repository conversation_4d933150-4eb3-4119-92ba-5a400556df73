package classes.com.samsung.magicinfo.webauthor2.properties;

import java.nio.file.Path;
import java.nio.file.Paths;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;

@PropertySources({@PropertySource({"classpath:default.magic.info.properties"}), @PropertySource(value = {"file:${MAGICINFO_PREMIUM_HOME}/conf/config.properties"}, ignoreResourceNotFound = true), @PropertySource(value = {"file:${WEB_AUTHOR_HOME}/conf/config.properties"}, ignoreResourceNotFound = true)})
@Configuration
public class MagicInfoProperties {
  @Value("${openAPI.Version}")
  private String openApiVersion;
  
  @Value("${wsrm.liteVersion}")
  private String wsrmLiteVersion;
  
  @Value("${wsrm.premiumVersion}")
  private String wsrmPremiumVersion;
  
  @Value("${webauthor.context}")
  private String webauthorContext;
  
  @Value("${webauthor.web_url}")
  private String webauthorWebUrl;
  
  @Value("${webauthor.web_url_public}")
  private String webauthorWebUrlPublic;
  
  @Value("${webauthor.ftp.host}")
  private String webauthorFtpHost;
  
  @Value("${webauthor.ftp.port}")
  private int webauthorFtpPort;
  
  @Value("${webauthor.ftp.sslport}")
  private int webauthorFtpSslPort;
  
  @Value("${webauthor.ftp.ssl}")
  private boolean webauthorFtpSsl;
  
  @Value("${webauthor.version}")
  private String webauthorVersion;
  
  @Value("${webauthor.development}")
  private String webauthorDevelopment;
  
  @Value("${webauthor.userExperience.enable}")
  private boolean userExperienceEnable = true;
  
  @Value("${webauthor.userExperience.storePath}")
  private String userExperienceStorePath;
  
  @Value("${webauthor.cookieName}")
  private String webauthorCookieName;
  
  @Value("${webauthor.upload.maxFilesQuantity}")
  private int webauthorUploadMaxFilesQuantity;
  
  @Value("${webauthor.mediaSlideLimit}")
  private int webauthorMediaSlideLimit;
  
  @Value("${webauthor.textPasteLimit}")
  private int webauthorTextPasteLimit;
  
  @Value("${webauthor.s2playerFlag}")
  private boolean webauthorS2PlayerFlag;
  
  @Value("${webauthor.maxZipSize}")
  private long maxZipSize;
  
  @Value("${webauthor.maxZipFilesQuantity}")
  private int maxZipFilesQuantity;
  
  @Value("${download.server.web.port}")
  private int downloadServerWebPort;
  
  @Value("${CONTENTS_HOME}")
  private String contentsHome;
  
  @Value("${CONTENTS_DIR}")
  private String contentsDir;
  
  @Value("${yesco.ui.enable}")
  private boolean yescoUiEnable;
  
  @Value("${VWT_HOME}")
  private String vwtHome;
  
  @Value("${webauthor.ssl.security.setting:DEFAULT}")
  private String sslSecuritySetting = "";
  
  @Value("${webauthor.previewRetention.days:10}")
  private int previewRetention;
  
  @Value("${keystore.identity.path}")
  private String keystoreIdentityPath;
  
  @Value("${keystore.identity.password}")
  private String keystoreIdentityPassword;
  
  private int waMajorVersion = 21;
  
  public String getOpenApiVersion() {
    return this.openApiVersion;
  }
  
  public String getWsrmLiteVersion() {
    return this.wsrmLiteVersion;
  }
  
  public String getWsrmPremiumVersion() {
    return this.wsrmPremiumVersion;
  }
  
  public String getWebauthorContext() {
    return this.webauthorContext;
  }
  
  public String getWebauthorWebUrl() {
    return this.webauthorWebUrl;
  }
  
  public String getWebauthorWebUrlPublic() {
    return this.webauthorWebUrlPublic;
  }
  
  public String getWebauthorFtpHost() {
    return this.webauthorFtpHost;
  }
  
  public int getWebauthorFtpPort() {
    return this.webauthorFtpPort;
  }
  
  public int getWebauthorFtpSslPort() {
    return this.webauthorFtpSslPort;
  }
  
  public boolean isWebauthorFtpSsl() {
    return this.webauthorFtpSsl;
  }
  
  public String getWebauthorVersion() {
    return this.webauthorVersion;
  }
  
  public String getWebauthorDevelopment() {
    return this.webauthorDevelopment;
  }
  
  public boolean getUserExperienceEnable() {
    return this.userExperienceEnable;
  }
  
  public String getUserExperienceStorePath() {
    if (this.userExperienceStorePath.isEmpty())
      this.userExperienceStorePath = "${MAGICINFO_PREMIUM_HOME}/webauthor/userExperience"; 
    return this.userExperienceStorePath;
  }
  
  public String getLocalWebAuthorPath() {
    return "${MAGICINFO_PREMIUM_HOME}/webauthor/";
  }
  
  public String getWebauthorCookieName() {
    return this.webauthorCookieName;
  }
  
  public int getWebauthorUploadMaxFilesQuantity() {
    return this.webauthorUploadMaxFilesQuantity;
  }
  
  public int getWebauthorMediaSlideLimit() {
    return this.webauthorMediaSlideLimit;
  }
  
  public int getWebauthorTextPasteLimit() {
    return this.webauthorTextPasteLimit;
  }
  
  public boolean isWebauthorS2PlayerFlag() {
    return this.webauthorS2PlayerFlag;
  }
  
  public long getMaxSizeOfZip() {
    return this.maxZipSize;
  }
  
  public int getMaxQuantitiyOfZipFiles() {
    return this.maxZipFilesQuantity;
  }
  
  public int getDownloadServerWebPort() {
    return this.downloadServerWebPort;
  }
  
  public String getContentsHome() {
    return this.contentsHome;
  }
  
  public String getContentsDir() {
    return this.contentsDir;
  }
  
  public boolean isYescoUiEnable() {
    return this.yescoUiEnable;
  }
  
  public String getVwtHome() {
    return this.vwtHome;
  }
  
  public String getSslSecuritySetting() {
    return this.sslSecuritySetting;
  }
  
  public Path getMagicInfoContentsLocationPath() {
    return Paths.get(this.contentsHome, new String[] { this.contentsDir });
  }
  
  public int getPreviewRetention() {
    return this.previewRetention;
  }
  
  public int getWebAuthorMajorVersion() {
    return this.waMajorVersion;
  }
  
  public String getKeystoreIdentityPath() {
    return this.keystoreIdentityPath;
  }
  
  public String getKeystoreIdentityPassword() {
    return this.keystoreIdentityPassword;
  }
}
