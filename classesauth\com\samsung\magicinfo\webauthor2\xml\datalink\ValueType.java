package classes.com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.datalink.FileInfoType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAnyElement;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlMixed;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.NONE)
@XmlType(name = "ValueType", propOrder = {"content", "fileInfo"})
public class ValueType {
  @XmlMixed
  @XmlAnyElement(lax = true)
  private String content;
  
  @XmlElement(name = "FileInfo")
  private FileInfoType fileInfo;
  
  public ValueType() {}
  
  public ValueType(String content) {
    this.content = content;
  }
  
  public ValueType(String content, FileInfoType fileInfo) {
    this.content = content;
    this.fileInfo = fileInfo;
  }
  
  public String getContent() {
    return this.content;
  }
  
  public void setContent(String content) {
    this.content = content;
  }
  
  public FileInfoType getFileInfo() {
    return this.fileInfo;
  }
  
  public void setFileInfo(FileInfoType fileInfo) {
    this.fileInfo = fileInfo;
  }
}
