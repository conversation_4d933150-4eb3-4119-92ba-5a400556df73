package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.Device;
import com.samsung.magicinfo.webauthor2.model.DeviceGroup;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupLayout;
import java.util.List;

public interface DeviceService {
  List<DeviceGroup> getDeviceGroupList();
  
  List<DeviceGroup> getDeviceGroupInfo(String paramString);
  
  List<Device> getDeviceList(String paramString);
  
  List<Device> getDeviceListWithDeviceType(String paramString);
  
  DeviceGroupLayout getVideowallLayoutPath(String paramString);
  
  Boolean hasWPlayerDevice();
}
