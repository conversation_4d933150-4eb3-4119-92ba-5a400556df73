package classes.com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.svg.FontDescription;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.util.List;
import org.springframework.web.multipart.MultipartFile;

public interface FontUploadService {
  FontDescription getFontDescription(String paramString);
  
  List<MediaSource> getUpdatedMediaSources(MultipartFile paramMultipartFile, String paramString, DeviceType paramDeviceType);
  
  String uploadFont(String paramString);
}
