package classes.com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;

public class NewFileInfo {
  private String newFileData;
  
  private MediaSource newMediaSource;
  
  @JsonCreator
  public NewFileInfo(@JsonProperty(value = "newFileData", required = true) String newFileData, @JsonProperty(value = "newMediaSource", required = true) MediaSource newMediaSource) {
    this.newFileData = newFileData;
    this.newMediaSource = newMediaSource;
  }
  
  public String getFileData() {
    return this.newFileData;
  }
  
  public void setFileData(String newFileData) {
    this.newFileData = newFileData;
  }
  
  public MediaSource getMediaSource() {
    return this.newMediaSource;
  }
  
  public void setMediaSource(MediaSource mediaSource) {
    this.newMediaSource = mediaSource;
  }
}
