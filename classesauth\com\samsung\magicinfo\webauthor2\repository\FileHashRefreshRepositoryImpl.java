package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.FileHashRefreshRepository;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.net.URI;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Repository;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

@Repository
public class FileHashRefreshRepositoryImpl implements FileHashRefreshRepository {
  private final RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Autowired
  public FileHashRefreshRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public void fileHashRefresh(String contentId, String fileId, String newFileHash) {
    Boolean isUrlAuthNotAllowed = this.userData.isUrlAuthNotAllowedToThisMisServletSession();
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/servlet/FileHashRefresh");
    if (false == isUrlAuthNotAllowed.booleanValue())
      uriComponentsBuilder.queryParam("userId", new Object[] { this.userData.getUserId() }).queryParam("passwd", new Object[] { this.userData.getToken() }); 
    URI uri = uriComponentsBuilder.build().encode().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.add("CID", contentId);
    headers.add("FILE_ID", fileId);
    headers.setContentType(MediaType.MULTIPART_FORM_DATA);
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    linkedMultiValueMap.add("fileHashValue", newFileHash);
    HttpEntity<MultiValueMap<String, String>> request = new HttpEntity(linkedMultiValueMap, (MultiValueMap)headers);
    this.restTemplate.exchange(uri, HttpMethod.POST, request, String.class);
  }
}
