package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.model.DatalinkServerEntityData;
import java.util.List;

public interface OpenAPIDataLinkRepository {
  List<DatalinkServerEntityData> getDataLinkServerEntities();
  
  void addDlkInfo(String paramString1, String paramString2, String paramString3, List<String> paramList, String paramString4);
}
