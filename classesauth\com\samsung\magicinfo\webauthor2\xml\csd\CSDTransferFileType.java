package classes.com.samsung.magicinfo.webauthor2.xml.csd;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;
import org.eclipse.persistence.oxm.annotations.XmlCDATA;

@XmlRootElement(name = "TransferFile")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransferFileType", propOrder = {"fileName", "fileSize", "playTime", "resolution", "storePath", "fileId", "fileHashValue", "mediaType", "startUpPage"})
public class CSDTransferFileType {
  @XmlAttribute(name = "type")
  private String type;
  
  @XmlAttribute(name = "supportfileitems")
  private boolean supportFileItems;
  
  @XmlAttribute(name = "reqIndex")
  private int reqIndex;
  
  @XmlElement(name = "FileName", required = true)
  @XmlCDATA
  private String fileName;
  
  @XmlElement(name = "FileSize")
  private long fileSize;
  
  @XmlElement(name = "StorePath")
  @XmlCDATA
  private String storePath;
  
  @XmlElement(name = "FileID")
  private String fileId;
  
  @XmlElement(name = "FileHashValue")
  private String fileHashValue;
  
  @XmlElement(name = "PlayTime")
  private String playTime;
  
  @XmlElement(name = "Resolution")
  private String resolution;
  
  @XmlElement(name = "MediaType", required = false)
  private String mediaType;
  
  @XmlElement(name = "StartUpPage", required = false)
  private String startUpPage;
  
  public void setFileName(String fileName) {
    this.fileName = fileName;
  }
  
  public String getPlayTime() {
    return this.playTime;
  }
  
  public void setPlayTime(String playTime) {
    this.playTime = playTime;
  }
  
  public String getResolution() {
    return this.resolution;
  }
  
  public void setResolution(String resolution) {
    this.resolution = resolution;
  }
  
  public void setFileSize(long fileSize) {
    this.fileSize = fileSize;
  }
  
  public void setStorePath(String storePath) {
    this.storePath = storePath;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public void setFileHashValue(String fileHashValue) {
    this.fileHashValue = fileHashValue;
  }
  
  public void setType(String type) {
    this.type = type;
  }
  
  public void setSupportFileItems(boolean supportFileItems) {
    this.supportFileItems = supportFileItems;
  }
  
  public void setReqIndex(int reqIndex) {
    this.reqIndex = reqIndex;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
  
  public String getStorePath() {
    return this.storePath;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileHashValue() {
    return this.fileHashValue;
  }
  
  public String getType() {
    return this.type;
  }
  
  public boolean isSupportFileItems() {
    return this.supportFileItems;
  }
  
  public int getReqIndex() {
    return this.reqIndex;
  }
  
  public String getMediaType() {
    return this.mediaType;
  }
  
  public void setMediaType(String mediaType) {
    this.mediaType = mediaType;
  }
  
  public String getStartUpPage() {
    return this.startUpPage;
  }
  
  public void setStartUpPage(String startUpPage) {
    this.startUpPage = startUpPage;
  }
}
