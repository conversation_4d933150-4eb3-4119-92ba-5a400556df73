package classes.com.samsung.magicinfo.webauthor2.util;

import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.util.ServerInfo;
import com.samsung.magicinfo.webauthor2.util.WeakTrustManager;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.net.URL;
import java.security.GeneralSecurityException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

@Component
public class WebResourceService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.util.WebResourceService.class);
  
  private WeakTrustManager weakTrustManager;
  
  private MagicInfoProperties magicInfoProperties;
  
  private ServerInfo serverInfo;
  
  @Autowired
  public WebResourceService(WeakTrustManager weakTrustManager, MagicInfoProperties magicInfoProperties, ServerInfo serverInfo) {
    this.weakTrustManager = weakTrustManager;
    this.magicInfoProperties = magicInfoProperties;
    this.serverInfo = serverInfo;
  }
  
  public void copyResourceToFile(String strURL, File file) throws IOException {
    int lastIndexOfMagicInfo = strURL.lastIndexOf("/" + this.magicInfoProperties.getWebauthorContext() + "/");
    String contentRelativePath = strURL.substring(lastIndexOfMagicInfo, strURL.length());
    try {
      URI localURI = new URI(this.serverInfo.getServerUrl());
      URI uri = UriComponentsBuilder.newInstance().scheme(localURI.getScheme()).host(localURI.getHost()).port(localURI.getPort()).path(contentRelativePath).build().encode().toUri();
      URL sourceURL = uri.toURL();
      logger.info("URL  [{}] replaced by [{}]", strURL, sourceURL);
      if (sourceURL.getProtocol().equals("https")) {
        copyHttpsURLToFile(sourceURL, file);
      } else {
        FileUtils.copyURLToFile(sourceURL, file);
      } 
    } catch (URISyntaxException e) {
      logger.error(e.getMessage());
    } 
  }
  
  private void copyHttpsURLToFile(URL sourceURL, File file) throws IOException {
    BufferedInputStream inputStream = null;
    try (BufferedOutputStream outputStream = new BufferedOutputStream(new FileOutputStream(file))) {
      SSLContext sc = SSLContext.getInstance("SSL");
      sc.init(null, this.weakTrustManager.getTrustManager(), new SecureRandom());
      HttpsURLConnection connection = (HttpsURLConnection)sourceURL.openConnection();
      connection.setSSLSocketFactory(sc.getSocketFactory());
      connection.setHostnameVerifier((HostnameVerifier)new Object(this));
      byte[] buffer = new byte[1024];
      inputStream = new BufferedInputStream(connection.getInputStream());
      int bytesRead;
      while ((bytesRead = inputStream.read(buffer)) != -1)
        outputStream.write(buffer, 0, bytesRead); 
    } catch (NoSuchAlgorithmException|java.security.KeyManagementException e) {
      throw new IOException("Error during web resource copying " + e.getMessage());
    } finally {
      if (inputStream != null)
        inputStream.close(); 
    } 
  }
}
