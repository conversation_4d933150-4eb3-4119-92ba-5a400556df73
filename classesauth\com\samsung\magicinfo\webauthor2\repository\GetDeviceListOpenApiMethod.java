package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceListResultListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetDeviceListOpenApiMethod extends OpenApiMethod<List<DeviceData>, DeviceListResponseData> {
  private final String userId;
  
  private final String token;
  
  private final String groupId;
  
  public GetDeviceListOpenApiMethod(RestTemplate restTemplate, String userId, String token, String groupId) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.groupId = groupId;
  }
  
  protected String getOpenApiClassName() {
    return "PremiumDeviceService";
  }
  
  protected String getOpenApiMethodName() {
    return "getDeviceList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("condition", "<DeviceCondition><groupId>" + this.groupId + "</groupId></DeviceCondition>");
    vars.put("deviceType", "ALL");
    return vars;
  }
  
  Class<DeviceListResponseData> getResponseClass() {
    return DeviceListResponseData.class;
  }
  
  List<DeviceData> convertResponseData(DeviceListResponseData responseData) {
    List<DeviceData> resultList;
    DeviceListResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      resultList = resultListData.getResultList();
    } else {
      resultList = new ArrayList<>();
    } 
    return resultList;
  }
}
