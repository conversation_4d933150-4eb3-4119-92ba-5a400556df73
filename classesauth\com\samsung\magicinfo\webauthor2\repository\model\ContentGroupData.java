package classes.com.samsung.magicinfo.webauthor2.repository.model;

import javax.xml.bind.annotation.XmlElement;

public class ContentGroupData {
  @XmlElement(name = "group_id")
  private int groupId;
  
  @XmlElement(name = "p_group_id")
  private int parentGroupId;
  
  @XmlElement(name = "group_depth")
  private int groupDepth;
  
  @XmlElement(name = "group_name")
  private String groupName;
  
  public int getGroupId() {
    return this.groupId;
  }
  
  public int getParentGroupId() {
    return this.parentGroupId;
  }
  
  public int getGroupDepth() {
    return this.groupDepth;
  }
  
  public String getGroupName() {
    return this.groupName;
  }
}
