package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseMediaTypeData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListMediaTypeData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetMediaTypeListOpenApiMethod extends OpenApiMethod<List<String>, ResponseMediaTypeData> {
  private final String token;
  
  private final String deviceType;
  
  public GetMediaTypeListOpenApiMethod(RestTemplate restTemplate, String token, DeviceType deviceType) {
    super(restTemplate);
    this.token = token;
    this.deviceType = deviceType.toString();
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getMediaTypeList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("deviceType", this.deviceType);
    return vars;
  }
  
  Class<ResponseMediaTypeData> getResponseClass() {
    return ResponseMediaTypeData.class;
  }
  
  List<String> convertResponseData(ResponseMediaTypeData responseMediaTypeData) {
    List<String> list;
    ResultListMediaTypeData resultListData = responseMediaTypeData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      list = resultListData.getResultList();
    } else {
      list = new ArrayList<>();
    } 
    return list;
  }
}
