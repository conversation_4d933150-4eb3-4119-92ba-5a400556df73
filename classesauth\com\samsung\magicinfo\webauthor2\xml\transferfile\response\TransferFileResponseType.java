package classes.com.samsung.magicinfo.webauthor2.xml.transferfile.response;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransferFileResponseType", propOrder = {"isNew", "reqFileId", "fileId", "fileOffset"})
public class TransferFileResponseType {
  @XmlElement(name = "IsNew")
  private boolean isNew;
  
  @XmlElement(name = "ReqFileID")
  private String reqFileId;
  
  @XmlElement(name = "FileID")
  private String fileId;
  
  @XmlElement(name = "FileOffset")
  private long fileOffset;
  
  @XmlAttribute
  private int reqIndex;
  
  public boolean isNew() {
    return this.isNew;
  }
  
  public void setNew(boolean isNew) {
    this.isNew = isNew;
  }
  
  public String getReqFileId() {
    return this.reqFileId;
  }
  
  public void setReqFileId(String reqFileId) {
    this.reqFileId = reqFileId;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public void setFileId(String fileId) {
    this.fileId = fileId;
  }
  
  public long getFileOffset() {
    return this.fileOffset;
  }
  
  public void setFileOffset(long fileOffset) {
    this.fileOffset = fileOffset;
  }
  
  public int getReqIndex() {
    return this.reqIndex;
  }
  
  public void setReqIndex(int reqIndex) {
    this.reqIndex = reqIndex;
  }
  
  public String toString() {
    return "TransferFileResponseType [isNew=" + this.isNew + ", reqFileId=" + this.reqFileId + ", fileId=" + this.fileId + ", reqIndex=" + this.reqIndex + "]";
  }
}
