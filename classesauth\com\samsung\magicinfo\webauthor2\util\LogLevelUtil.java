package classes.com.samsung.magicinfo.webauthor2.util;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.filter.ThresholdFilter;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.Appender;
import ch.qos.logback.core.filter.Filter;
import org.slf4j.LoggerFactory;

public final class LogLevelUtil {
  public static void setDefaultLogLevel() {
    setLogLevel(Level.INFO, Level.DEBUG, Level.WARN);
  }
  
  public static void setLogLevel(String level) {
    Level newLevel = Level.valueOf(level);
    setLogLevel(newLevel, newLevel, newLevel);
  }
  
  public static Level getLogLevel() {
    Logger logger = (Logger)LoggerFactory.getLogger("ROOT");
    return logger.getLevel();
  }
  
  private static void setLogLevel(Level rootLevel, Level consoleLevel, Level fileLevel) {
    Logger logger = (Logger)LoggerFactory.getLogger("ROOT");
    logger.setLevel(rootLevel);
    Appender<ILoggingEvent> consoleAppender = logger.getAppender("CONSOLE");
    if (consoleAppender != null)
      setNewLevelInAppender(consoleAppender, consoleLevel); 
    Appender<ILoggingEvent> fileAppender = logger.getAppender("FILE");
    if (fileAppender != null)
      setNewLevelInAppender(fileAppender, fileLevel); 
  }
  
  private static void setNewLevelInAppender(Appender<ILoggingEvent> consoleAppender, Level consoleLevel) {
    consoleAppender.clearAllFilters();
    ThresholdFilter defaultConsoleFilter = new ThresholdFilter();
    defaultConsoleFilter.setLevel(consoleLevel.toString());
    consoleAppender.addFilter((Filter)defaultConsoleFilter);
  }
}
