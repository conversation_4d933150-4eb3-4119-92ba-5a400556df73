package classes.com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.ResultListFileTypesData;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class ResponseFileTypesData implements Serializable {
  private static final long serialVersionUID = -7570944763572605315L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private ResultListFileTypesData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public ResultListFileTypesData getResponseClass() {
    return this.responseClass;
  }
}
