package classes.com.samsung.magicinfo.webauthor2.xml.transferfile.response;

import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFileResponseType;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

@XmlRootElement(name = "Content")
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransferFilesResponseType", propOrder = {"transferFiles"})
public class TransferFilesResponseType {
  @XmlElement(name = "TransferFile")
  private List<TransferFileResponseType> transferFiles;
  
  @XmlAttribute(name = "cid")
  private String contentId;
  
  public List<TransferFileResponseType> getTransferFiles() {
    return this.transferFiles;
  }
  
  public void setTransferFiles(List<TransferFileResponseType> transferFiles) {
    this.transferFiles = transferFiles;
  }
  
  public String getContentId() {
    return this.contentId;
  }
  
  public void setContentId(String contentId) {
    this.contentId = contentId;
  }
  
  public String toString() {
    return "TransferFilesResponseType [transferFiles=" + this.transferFiles + "]";
  }
}
