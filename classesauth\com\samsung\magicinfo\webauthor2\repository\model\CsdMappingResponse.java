package classes.com.samsung.magicinfo.webauthor2.repository.model;

import org.springframework.http.HttpStatus;

public class CsdMappingResponse {
  private HttpStatus statusCode;
  
  private String versionId;
  
  private String contentDuplicate;
  
  private String body;
  
  public CsdMappingResponse(HttpStatus statusCode, String versionId, String contentDuplicate, String body) {
    this.statusCode = statusCode;
    this.versionId = versionId;
    this.contentDuplicate = contentDuplicate;
    this.body = body;
  }
  
  public HttpStatus getStatusCode() {
    return this.statusCode;
  }
  
  public String getVersionId() {
    return this.versionId;
  }
  
  public String getContentDuplicate() {
    return this.contentDuplicate;
  }
  
  public String getBody() {
    return this.body;
  }
}
