package classes.com.samsung.magicinfo.webauthor2.config;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import com.samsung.magicinfo.webauthor2.model.Tag;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.repository.inmemory.DataSourceH2;
import com.samsung.magicinfo.webauthor2.repository.inmemory.dao.PreviewUsageDao;
import com.samsung.magicinfo.webauthor2.repository.inmemory.dao.PreviewUsageDaoImpl;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertDataData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ConvertTableResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.datalink.ResponseConvertTableData;
import com.samsung.magicinfo.webauthor2.repository.model.tag.TagResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.tag.TagResultListData;
import com.samsung.magicinfo.webauthor2.util.AdaptableTrustManager;
import com.samsung.magicinfo.webauthor2.util.CleanPreviewFolder;
import com.samsung.magicinfo.webauthor2.util.ResetLoggingLevel;
import com.samsung.magicinfo.webauthor2.util.WeakTrustManager;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDFileType;
import com.samsung.magicinfo.webauthor2.xml.csd.CSDTransferFileType;
import com.samsung.magicinfo.webauthor2.xml.datalink.DataLinkContentMetaType;
import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableListType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.TransferFileType;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.security.cert.CertificateException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.sql.DataSource;
import javax.xml.parsers.SAXParser;
import javax.xml.parsers.SAXParserFactory;
import org.apache.http.client.HttpClient;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.http.client.ClientHttpRequestFactory;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.client.RestTemplate;
import org.springframework.xml.xpath.Jaxp13XPathTemplate;
import org.springframework.xml.xpath.XPathOperations;

@Configuration
@EnableScheduling
@EnableAsync
@Import({MagicInfoProperties.class, DataSourceH2.class})
public class AppConfig {
  @Autowired
  DataSource dataSource;
  
  @Bean
  public MagicInfoProperties magicInfoProperties() {
    return new MagicInfoProperties();
  }
  
  @Bean
  public JdbcTemplate getJdbcTemplate() {
    return new JdbcTemplate(this.dataSource);
  }
  
  @Bean
  public Executor taskExecutor() {
    ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
    executor.setCorePoolSize(2);
    executor.setMaxPoolSize(2);
    executor.setQueueCapacity(500);
    executor.setThreadNamePrefix("WA");
    executor.initialize();
    return (Executor)executor;
  }
  
  @Bean(name = {"dataLinkRestTemplate"})
  public RestTemplate dataLinkRestTemplate() throws Exception {
    return new RestTemplate((ClientHttpRequestFactory)clientHttpRequestFactory());
  }
  
  @Bean
  public PreviewUsageDao previewUsageDao() {
    return (PreviewUsageDao)new PreviewUsageDaoImpl(getJdbcTemplate());
  }
  
  @Bean
  public HttpComponentsClientHttpRequestFactory clientHttpRequestFactory() throws IOException, NoSuchAlgorithmException, KeyStoreException, KeyManagementException, CertificateException {
    TrustManager[] adaptableTM = adaptableTrustManager().getTrustManager(magicInfoProperties().getSslSecuritySetting(), 
        magicInfoProperties().getKeystoreIdentityPath(), magicInfoProperties().getKeystoreIdentityPassword());
    SSLContext sc = SSLContext.getInstance("SSL");
    sc.init(null, adaptableTM, new SecureRandom());
    SSLConnectionSocketFactory csf = new SSLConnectionSocketFactory(sc, (HostnameVerifier)new NoopHostnameVerifier());
    CloseableHttpClient httpClient = HttpClients.custom().setSSLSocketFactory((LayeredConnectionSocketFactory)csf).setSSLHostnameVerifier((HostnameVerifier)new NoopHostnameVerifier()).build();
    HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
    requestFactory.setHttpClient((HttpClient)httpClient);
    requestFactory.setConnectionRequestTimeout(10000);
    requestFactory.setReadTimeout(10000);
    requestFactory.setConnectTimeout(10000);
    requestFactory.setBufferRequestBody(false);
    return requestFactory;
  }
  
  @Bean
  public XPathOperations xpathTemplate() {
    return (XPathOperations)new Jaxp13XPathTemplate();
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2Marshaller() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ContentSearchCriteria.class, ResponseData.class, ResultListData.class, ContentData.class, Content.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerTag() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { TagResultListData.class, TagResponseData.class, Tag.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerConvertTableList() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ConvertDataData.class, ConvertTableData.class, ConvertTableResultListData.class, ResponseConvertTableData.class, ConvertTable.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerConvertTable() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ConvertDataData.class, ConvertTableData.class, OpenAPIResponseData.class, ConvertTable.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerFragment() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ContentSearchCriteria.class, ConvertTableData.class, ConvertDataData.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForDLK() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { DataLinkContentMetaType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForTransferFile() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { TransferFileType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForResponseTransferFile() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { TransferFilesResponseType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForDlkInfo() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.TRUE);
    jaxb2Marshaller.setClassesToBeBound(new Class[] { ConvertTableListType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public Jaxb2Marshaller jaxb2MarshallerForCSD() {
    Jaxb2Marshaller jaxb2Marshaller = new Jaxb2Marshaller();
    Map<String, Object> props = new HashMap<>();
    props.put("jaxb.formatted.output", Boolean.TRUE);
    props.put("jaxb.fragment", Boolean.FALSE);
    props.put("jaxb.encoding", "UTF-8");
    jaxb2Marshaller.setClassesToBeBound(new Class[] { CSDTransferFileType.class, CSDFileType.class });
    jaxb2Marshaller.setMarshallerProperties(props);
    return jaxb2Marshaller;
  }
  
  @Bean
  public WeakTrustManager weakTrustManager() {
    return new WeakTrustManager();
  }
  
  @Bean
  public AdaptableTrustManager adaptableTrustManager() {
    return new AdaptableTrustManager();
  }
  
  @Bean
  public SAXParser saxParser() throws Exception {
    SAXParserFactory spf = SAXParserFactory.newInstance();
    spf.setNamespaceAware(true);
    return spf.newSAXParser();
  }
  
  @Bean
  public static PropertySourcesPlaceholderConfigurer propertySourcesPlaceholderConfigurer() {
    return new PropertySourcesPlaceholderConfigurer();
  }
  
  @Bean
  public ResetLoggingLevel resetLoggingLevel() {
    return new ResetLoggingLevel();
  }
  
  @Bean
  public CleanPreviewFolder cleanPreviewFolder() {
    return new CleanPreviewFolder(previewUsageDao(), magicInfoProperties().getPreviewRetention());
  }
}
