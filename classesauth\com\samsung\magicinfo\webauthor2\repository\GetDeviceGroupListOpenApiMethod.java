package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupListResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.device.DeviceGroupListResultListData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetDeviceGroupListOpenApiMethod extends OpenApiMethod<List<DeviceGroupData>, DeviceGroupListResponseData> {
  private final String userId;
  
  private final String token;
  
  public GetDeviceGroupListOpenApiMethod(RestTemplate restTemplate, String userId, String token) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "PremiumDeviceService";
  }
  
  protected String getOpenApiMethodName() {
    return "getDeviceGroupList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("recursive", "false");
    vars.put("deviceType", "ALL");
    return vars;
  }
  
  Class<DeviceGroupListResponseData> getResponseClass() {
    return DeviceGroupListResponseData.class;
  }
  
  List<DeviceGroupData> convertResponseData(DeviceGroupListResponseData responseData) {
    List<DeviceGroupData> resultList;
    DeviceGroupListResultListData resultListData = responseData.getResponseClass();
    if (resultListData != null && resultListData.getResultList() != null) {
      resultList = resultListData.getResultList();
    } else {
      resultList = new ArrayList<>();
    } 
    return resultList;
  }
}
