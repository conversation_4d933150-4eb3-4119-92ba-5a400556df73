package classes.com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.LFDContent;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class LFDContentResource extends Resource<LFDContent> implements Serializable {
  private static final long serialVersionUID = 1L;
  
  public LFDContentResource(LFDContent content, Iterable<Link> links) {
    super(content, links);
  }
  
  public LFDContentResource(LFDContent content, Link... links) {
    super(content, links);
  }
}
