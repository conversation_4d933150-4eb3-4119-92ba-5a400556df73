package classes.com.samsung.magicinfo.webauthor2.model;

import com.google.common.base.Optional;

public final class FileMetaInfo {
  private String length;
  
  private String width;
  
  private String height;
  
  public FileMetaInfo() {}
  
  public FileMetaInfo(String length, String width, String height) {
    this.length = length;
    this.width = width;
    this.height = height;
  }
  
  public Optional<String> getLength() {
    return Optional.of(this.length);
  }
  
  public void setLength(String length) {
    this.length = length;
  }
  
  public Optional<String> getWidth() {
    return Optional.of(this.width);
  }
  
  public void setWidth(String width) {
    this.width = width;
  }
  
  public Optional<String> getHeight() {
    return Optional.of(this.height);
  }
  
  public void setHeight(String height) {
    this.height = height;
  }
}
