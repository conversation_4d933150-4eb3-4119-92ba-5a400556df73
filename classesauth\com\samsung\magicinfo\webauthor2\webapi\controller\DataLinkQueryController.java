package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.repository.CannotGetDataLinkDescriptorException;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotGetDataLinkTableInfoException;
import com.samsung.magicinfo.webauthor2.exception.repository.CannotGetDataLinkTablesException;
import com.samsung.magicinfo.webauthor2.exception.service.CannotFindDataLinkServerException;
import com.samsung.magicinfo.webauthor2.exception.service.CannotFindDataLinkTableException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.model.DataLinkTableRow;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.service.DataLinkSaveService;
import com.samsung.magicinfo.webauthor2.service.DataLinkService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.DataLinkDescriptorResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.assembler.DataLinkServerResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.assembler.DataLinkTableResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.assembler.DataLinkTableRowResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkDescriptorResource;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkServerResource;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkTableResource;
import com.samsung.magicinfo.webauthor2.webapi.resource.DataLinkTableRowResource;
import java.io.IOException;
import java.util.List;
import javax.servlet.ServletException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/datalink"})
public class DataLinkQueryController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController.class);
  
  private final DataLinkServerResourceAssembler dataLinkServerResourceAssembler;
  
  private final DataLinkTableResourceAssembler dataLinkTableResourceAssembler;
  
  private final DataLinkTableRowResourceAssembler dataLinkTableRowResourceAssembler;
  
  private final DataLinkDescriptorResourceAssembler dataLinkDescriptorResourceAssembler;
  
  private final DataLinkService dataLinkService;
  
  private final DataLinkSaveService dataLinkSaveService;
  
  @Autowired
  public DataLinkQueryController(DataLinkServerResourceAssembler dataLinkServerResourceAssembler, DataLinkTableResourceAssembler dataLinkTableResourceAssembler, DataLinkTableRowResourceAssembler dataLinkTableRowResourceAssembler, DataLinkDescriptorResourceAssembler dataLinkDescriptorResourceAssembler, DataLinkService dataLinkService, DataLinkSaveService dataLinkSaveService) {
    this.dataLinkServerResourceAssembler = dataLinkServerResourceAssembler;
    this.dataLinkTableResourceAssembler = dataLinkTableResourceAssembler;
    this.dataLinkTableRowResourceAssembler = dataLinkTableRowResourceAssembler;
    this.dataLinkDescriptorResourceAssembler = dataLinkDescriptorResourceAssembler;
    this.dataLinkService = dataLinkService;
    this.dataLinkSaveService = dataLinkSaveService;
  }
  
  @GetMapping({"/servers"})
  public HttpEntity<List<DataLinkServerResource>> getDataLinkServerList() {
    List<DataLinkServer> dataLinkServerList = this.dataLinkService.getDataLinkServerList();
    return (HttpEntity<List<DataLinkServerResource>>)ResponseEntity.ok(this.dataLinkServerResourceAssembler.toResources(dataLinkServerList));
  }
  
  @GetMapping({"/serverListCached"})
  public HttpEntity<List<DataLinkServerResource>> getDataLinkServerListCached() {
    List<DataLinkServer> dataLinkServerList = this.dataLinkService.getDataLinkServerListCached();
    return (HttpEntity<List<DataLinkServerResource>>)ResponseEntity.ok(this.dataLinkServerResourceAssembler.toResources(dataLinkServerList));
  }
  
  @RequestMapping(value = {"/servers/{serverName}/tables"}, method = {RequestMethod.GET})
  public HttpEntity<List<DataLinkTableResource>> getDatalinkTables(@PathVariable String serverName, @RequestParam(required = false, defaultValue = "datalink") String dataType) {
    List<DataLinkTable> dataLinkTables = this.dataLinkService.getDataLinkServerTables(serverName, dataType);
    return (HttpEntity<List<DataLinkTableResource>>)ResponseEntity.ok(this.dataLinkTableResourceAssembler.toResources(dataLinkTables));
  }
  
  @RequestMapping(value = {"/servers/{serverName}/services"}, method = {RequestMethod.GET})
  public HttpEntity<List<String>> getDatalinkServices(@PathVariable String serverName, @RequestParam(required = false, defaultValue = "datalink") String dataType) {
    List<String> dataLinkServices = this.dataLinkService.getDataLinkServerServices(serverName, dataType);
    return (HttpEntity<List<String>>)ResponseEntity.ok(dataLinkServices);
  }
  
  @RequestMapping(value = {"/servers/{serverName}/services/{serviceName}"}, method = {RequestMethod.GET})
  public HttpEntity<List<DataLinkTableResource>> getDatalinkServicesTables(@PathVariable String serverName, @PathVariable String serviceName, @RequestParam(required = false, defaultValue = "datalink") String dataType) {
    List<DataLinkTable> dataLinkTables = this.dataLinkService.getDataLinkServerTablesForService(serverName, serviceName, dataType);
    return (HttpEntity<List<DataLinkTableResource>>)ResponseEntity.ok(this.dataLinkTableResourceAssembler.toResources(dataLinkTables));
  }
  
  @RequestMapping(value = {"/servers/{serverName}/tables/{dynaName}"}, method = {RequestMethod.GET})
  public HttpEntity<List<DataLinkTableRowResource>> getDatalinkTableInfo(@PathVariable String serverName, @PathVariable String dynaName) {
    List<DataLinkTableRow> tableRows = this.dataLinkService.getDataLinkServerTableRows(serverName, dynaName);
    return (HttpEntity<List<DataLinkTableRowResource>>)ResponseEntity.ok(this.dataLinkTableRowResourceAssembler.toResources(tableRows));
  }
  
  @RequestMapping(value = {"/model/{contentId}"}, method = {RequestMethod.GET})
  public HttpEntity<DataLinkDescriptorResource> getDataLinkDescriptor(@PathVariable String contentId) {
    DataLinkDescriptor dataLinkDescriptor = this.dataLinkService.getDataLinkDescriptor(contentId);
    return (HttpEntity<DataLinkDescriptorResource>)ResponseEntity.ok(this.dataLinkDescriptorResourceAssembler.toResource(dataLinkDescriptor));
  }
  
  @PostMapping({"/model"})
  public HttpEntity<UploadResponse> uploadDataLinkDescriptor(@RequestBody DataLinkDescriptor dataLinkDescriptor) throws UploaderException, IOException, ServletException {
    this.dataLinkSaveService.saveLocalDataLinkDescriptor(dataLinkDescriptor);
    return (HttpEntity<UploadResponse>)ResponseEntity.ok(new UploadResponse(HttpStatus.OK.value(), this.dataLinkSaveService.upload(dataLinkDescriptor)));
  }
  
  @ExceptionHandler({CannotFindDataLinkServerException.class, CannotFindDataLinkTableException.class, CannotGetDataLinkDescriptorException.class})
  public HttpEntity<Void> dataLinkTableNotFound(RuntimeException ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<Void>)ResponseEntity.notFound().build();
  }
  
  @ExceptionHandler({CannotGetDataLinkTablesException.class, CannotGetDataLinkTableInfoException.class})
  public HttpEntity<Void> cannotDownloadDataLinkTableFromLink(RuntimeException ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<Void>)ResponseEntity.status(HttpStatus.METHOD_NOT_ALLOWED).build();
  }
  
  @ExceptionHandler({UploaderException.class})
  public HttpEntity<UploadResponse> uploaderExceptionHandler(UploaderException ex) {
    logger.error(ex.getMessage(), (Throwable)ex);
    UploadResponse response = new UploadResponse(ex.getErrorCode(), ex.getMessage());
    return (HttpEntity<UploadResponse>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
  }
  
  @ExceptionHandler({Exception.class})
  public HttpEntity<Void> generalExceptionHandler(Exception ex) {
    logger.error(ex.getMessage(), ex);
    return (HttpEntity<Void>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
  }
}
