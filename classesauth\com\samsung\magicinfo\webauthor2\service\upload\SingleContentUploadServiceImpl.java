package classes.com.samsung.magicinfo.webauthor2.service.upload;

import com.google.common.base.Joiner;
import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import com.samsung.magicinfo.webauthor2.service.CSDFileService;
import com.samsung.magicinfo.webauthor2.service.CidMappingService;
import com.samsung.magicinfo.webauthor2.service.upload.JobStateService;
import com.samsung.magicinfo.webauthor2.service.upload.SingleContentUploadService;
import com.samsung.magicinfo.webauthor2.service.upload.UploadHelperService;
import com.samsung.magicinfo.webauthor2.service.upload.UploadService;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import com.samsung.magicinfo.webauthor2.util.UserData;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Date;
import java.util.List;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class SingleContentUploadServiceImpl implements SingleContentUploadService {
  private MultipartFilenameValidator multipartFilenameValidator;
  
  private CidMappingService cidMappingService;
  
  private CSDFileService csdFileService;
  
  private UploadService uploadService;
  
  private UploadHelperService uploadHelperService;
  
  private JobStateService jobStateService;
  
  private ServletContext servletContext;
  
  private UserData userData;
  
  @Autowired
  public SingleContentUploadServiceImpl(MultipartFilenameValidator multipartFilenameValidator, CidMappingService cidMappingService, CSDFileService csdFileService, UploadService uploadService, UploadHelperService uploadHelperService, JobStateService jobStateService, ServletContext servletContext, UserData userData) {
    this.multipartFilenameValidator = multipartFilenameValidator;
    this.cidMappingService = cidMappingService;
    this.csdFileService = csdFileService;
    this.uploadService = uploadService;
    this.uploadHelperService = uploadHelperService;
    this.jobStateService = jobStateService;
    this.servletContext = servletContext;
    this.userData = userData;
  }
  
  public String upload(MultipartFile fileItem, DeviceType deviceType) throws UploaderException, FileItemValidationException, IOException {
    this.multipartFilenameValidator.validateFileItem(fileItem);
    Path workingDirectory = initializeDirectoryStructure();
    List<MediaSource> mediaSources = this.uploadHelperService.prepareFilesToUpload(fileItem, workingDirectory);
    return uploadContent(mediaSources, workingDirectory, deviceType);
  }
  
  public String upload(String filePath, DeviceType deviceType) throws UploaderException, IOException {
    Path workingDirectory = initializeDirectoryStructure();
    Path realFilePath = Paths.get(this.servletContext.getRealPath(filePath), new String[0]);
    List<MediaSource> mediaSources = this.uploadHelperService.prepareFilesToUpload(realFilePath, workingDirectory);
    return uploadContent(mediaSources, workingDirectory, deviceType);
  }
  
  private String uploadContent(List<MediaSource> mediaSources, Path workingDirectory, DeviceType deviceType) {
    String contentId = this.cidMappingService.cidMapping();
    String csdXml = this.csdFileService.generateCSD(mediaSources, contentId, deviceType);
    TransferFilesResponseType responseFromCsdMapping = this.csdFileService.postSingleFileCsdToMips(csdXml, contentId);
    List<MediaSource> updatedMediaSources = this.csdFileService.updateMediaSources(mediaSources, responseFromCsdMapping);
    try {
      this.uploadService.upload(updatedMediaSources, contentId);
      boolean contentIsDuplicate = false;
      this.jobStateService.jobStateSuccess(this.userData.getUserId(), this.userData.getToken(), contentId, "1", contentIsDuplicate);
      return contentId;
    } catch (UploaderException ex) {
      this.jobStateService.jobStateFail(this.userData.getUserId(), this.userData.getToken(), contentId, "1");
      throw new UploaderException(ex.getErrorCode(), ex.getMessage());
    } finally {
      cleanUpDirectoryStructure(workingDirectory);
    } 
  }
  
  private Path initializeDirectoryStructure() {
    try {
      String insertContents = this.servletContext.getRealPath("insertContents");
      String userDirectory = Joiner.on("_").join(this.userData.getUserId(), Long.toString((new Date()).getTime()), new Object[0]);
      Path workingDirectory = Paths.get(insertContents, new String[] { userDirectory });
      if (Files.notExists(workingDirectory, new java.nio.file.LinkOption[0]))
        Files.createDirectories(workingDirectory, (FileAttribute<?>[])new FileAttribute[0]); 
      return workingDirectory;
    } catch (IOException e) {
      throw new UploaderException(500, "Error during file structure initialization");
    } 
  }
  
  private void cleanUpDirectoryStructure(Path workingDirectory) {
    try {
      FileUtils.deleteDirectory(workingDirectory.toFile());
    } catch (IOException e) {
      throw new UploaderException(500, "Error during clean up after file upload");
    } 
  }
}
