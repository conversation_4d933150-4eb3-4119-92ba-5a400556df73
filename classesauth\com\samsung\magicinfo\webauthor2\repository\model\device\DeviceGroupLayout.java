package classes.com.samsung.magicinfo.webauthor2.repository.model.device;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;

@XmlAccessorType(XmlAccessType.FIELD)
public class DeviceGroupLayout {
  @XmlElement(name = "vwt_id")
  private String vwt_id = "";
  
  @XmlElement(name = "vwt_file_name")
  private String vwt_file_name = "";
  
  public String getVwtId() {
    return this.vwt_id;
  }
  
  public void setVwtId(String vwt_id) {
    this.vwt_id = vwt_id;
  }
  
  public String getVwtFileName() {
    return this.vwt_file_name;
  }
  
  public void setVwtFileName(String vwt_file_name) {
    this.vwt_file_name = vwt_file_name;
  }
  
  public DeviceGroupLayout(String vwt_id, String vwt_file_name) {
    this.vwt_id = vwt_id;
    this.vwt_file_name = vwt_file_name;
  }
  
  public DeviceGroupLayout() {}
}
