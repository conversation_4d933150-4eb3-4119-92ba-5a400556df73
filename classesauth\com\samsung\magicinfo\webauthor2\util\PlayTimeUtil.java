package classes.com.samsung.magicinfo.webauthor2.util;

import com.google.common.base.Splitter;
import java.util.Iterator;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public final class PlayTimeUtil {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.util.PlayTimeUtil.class);
  
  public static Double convertPlayTime(String playTime) {
    Double duration = Double.valueOf(0.0D);
    int playTimeStringLength = 12;
    String timeFormatSeparator = ":";
    if (StringUtils.isEmpty(playTime) || playTime.length() > 12)
      return duration; 
    try {
      Iterator<String> timeIterator = Splitter.on(":").split(playTime).iterator();
      long hours = Long.parseLong(timeIterator.next());
      long minutes = Long.parseLong(timeIterator.next());
      double seconds = Double.parseDouble(timeIterator.next());
      duration = Double.valueOf(seconds + (60L * minutes) + (3600L * hours));
    } catch (Exception e) {
      logger.error(e.getMessage());
    } 
    return duration;
  }
  
  public static String covertPlayTimeFromSeconds(double totalSeconds) {
    int hours = (int)(totalSeconds / 3600.0D);
    int minutes = (int)(totalSeconds % 3600.0D / 60.0D);
    int seconds = (int)(totalSeconds % 60.0D);
    int milliSeconds = (int)Math.round(totalSeconds % 1.0D * 100.0D);
    StringBuilder time = new StringBuilder();
    time.append(String.format("%02d", new Object[] { Integer.valueOf(hours) }));
    time.append(":");
    time.append(String.format("%02d", new Object[] { Integer.valueOf(minutes) }));
    time.append(":");
    time.append(String.format("%02d", new Object[] { Integer.valueOf(seconds) }));
    if (milliSeconds > 0) {
      time.append(".");
      time.append(String.format("%02d", new Object[] { Integer.valueOf(milliSeconds) }));
    } 
    return time.toString();
  }
}
