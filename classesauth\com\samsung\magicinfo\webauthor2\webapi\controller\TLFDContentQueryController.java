package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.service.TLFDContentService;
import com.samsung.magicinfo.webauthor2.webapi.assembler.ContentResourceAssembler;
import com.samsung.magicinfo.webauthor2.webapi.resource.ContentResource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.web.PageableDefault;
import org.springframework.data.web.PagedResourcesAssembler;
import org.springframework.hateoas.PagedResources;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.http.HttpEntity;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/TLFDContents"})
public class TLFDContentQueryController {
  private final TLFDContentService tlfdContentService;
  
  private final ContentResourceAssembler contentResourceAssembler;
  
  @Autowired
  public TLFDContentQueryController(TLFDContentService tlfdContentService, ContentResourceAssembler contentResourceAssembler) {
    this.tlfdContentService = tlfdContentService;
    this.contentResourceAssembler = contentResourceAssembler;
  }
  
  @GetMapping
  public HttpEntity<PagedResources<ContentResource>> getContentsWithTLFDMediaType(@PageableDefault(size = 100) Pageable pageable, @RequestParam String playerType, PagedResourcesAssembler<Content> assembler) {
    Page<Content> page = this.tlfdContentService.getTLFDContentList(pageable, DeviceType.valueOf(playerType));
    PagedResources<ContentResource> contentResources = assembler.toResource(page, (ResourceAssembler)this.contentResourceAssembler);
    return (HttpEntity<PagedResources<ContentResource>>)ResponseEntity.ok(contentResources);
  }
}
