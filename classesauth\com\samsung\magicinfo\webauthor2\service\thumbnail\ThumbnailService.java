package classes.com.samsung.magicinfo.webauthor2.service.thumbnail;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.nio.file.Path;

public interface ThumbnailService {
  Path createProjectThumbnail(String paramString1, String paramString2, Boolean paramBoolean1, Boolean paramBoolean2) throws UploaderException;
  
  Path createContentThumbnail(MediaSource paramMediaSource, Path paramPath) throws UploaderException;
}
