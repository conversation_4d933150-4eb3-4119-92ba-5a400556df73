package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.WebContentValidationException;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

public interface WebContentValidationService {
  void validateWebContentInZip(MultipartFile paramMultipartFile, String paramString) throws IOException, WebContentValidationException, FileItemValidationException;
  
  void validateWebContentInZip(String paramString1, String paramString2) throws <PERSON>OEx<PERSON>, WebContentValidationException, FileItemValidationException;
}
