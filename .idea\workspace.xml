<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="1a31cd98-c6cd-4c5b-820f-3e44cef000f5" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\software\apache-maven-3.9.5\maven_resp" />
        <option name="mavenHome" value="$PROJECT_DIR$/../../../../../../../software/apache-maven-3.9.5" />
        <option name="userSettingsFile" value="D:\software\apache-maven-3.9.5\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="30d7uEMaD447xC5CgLrp1Im9BUk" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/AAA/代码审计/源码/2025/MagicINFO/1060/src-server1040",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.15",
    "project.structure.side.proportion": "0.46896553",
    "settings.editor.selected.configurable": "MavenSettings",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="1a31cd98-c6cd-4c5b-820f-3e44cef000f5" name="更改" comment="" />
      <created>1753945817551</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1753945817551</updated>
      <workItem from="1753945818318" duration="4968000" />
      <workItem from="1754014053496" duration="7145000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>