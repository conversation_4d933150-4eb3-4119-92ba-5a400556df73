package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.repository.OpenAPISystemInfoRepository;
import com.samsung.magicinfo.webauthor2.repository.model.ServerSystemInfoData;
import com.samsung.magicinfo.webauthor2.service.UserExperienceService;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class UserExperienceServiceImpl implements UserExperienceService {
  private final Logger LOGGER = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.UserExperienceServiceImpl.class);
  
  private MagicInfoProperties magicInfoProperties;
  
  private OpenAPISystemInfoRepository openAPISystemInfoRepository;
  
  SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd", Locale.ENGLISH);
  
  SimpleDateFormat dateTimeFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssZZZ", Locale.ENGLISH);
  
  private final String fileHeader = "KPI.WebAuthor.";
  
  private final String fileExtension = ".json";
  
  private final String keyTime = "time";
  
  private final String keyType = "type";
  
  private final String keyUid = "uid";
  
  private final String keyCategory = "category";
  
  private String hardwareUniqueId = "";
  
  private StringBuilder experienceStoreBuffer = new StringBuilder("");
  
  private int experienceStoreCount = 0;
  
  private long bufferStoreStartTick = 0L;
  
  @Autowired
  public UserExperienceServiceImpl(MagicInfoProperties magicInfoProperties, OpenAPISystemInfoRepository openAPISystemInfoRepository) {
    this.magicInfoProperties = magicInfoProperties;
    this.openAPISystemInfoRepository = openAPISystemInfoRepository;
  }
  
  private boolean isNecessaryToWriteFileNow(int countByThisProcess) {
    int sizeToSaveFileAtOneTime = 5242880;
    if (this.experienceStoreBuffer.length() > 5242880)
      return true; 
    long maxBufferAgeInMS = 10000L;
    if (System.currentTimeMillis() - this.bufferStoreStartTick > 10000L)
      return true; 
    boolean hasNextSaveProcessAfterMe = (this.experienceStoreCount != countByThisProcess);
    if (hasNextSaveProcessAfterMe)
      return false; 
    int waitForNextProcessTryCount = 2;
    for (int i = 0; i < waitForNextProcessTryCount; i++) {
      sleep(50);
      hasNextSaveProcessAfterMe = (this.experienceStoreCount != countByThisProcess);
      if (hasNextSaveProcessAfterMe)
        return false; 
    } 
    return true;
  }
  
  public int addToBuffer(List<String> expernences) {
    int experienceBufferCount = saveExperience(SaveTo.REQUEST_TO_BUFFER, getExperiencesInFormat(expernences));
    return experienceBufferCount;
  }
  
  public int saveBufferToFileNotTooFrequently(int experienceBufferCount) throws IOException {
    if (false == hasEnoughDiskStorage().booleanValue())
      return 0; 
    if (false == isNecessaryToWriteFileNow(experienceBufferCount))
      return 0; 
    int countSavedToFile = saveExperience(SaveTo.BUFFER_TO_FILE, "");
    return countSavedToFile;
  }
  
  private String getExperiencesInFormat(List<String> dataList) {
    StringBuilder sb = new StringBuilder("");
    String commaForEachLineToKeepJsonFormat = ",";
    sb.append(getHeader());
    for (String data : dataList)
      sb.append(data).append(commaForEachLineToKeepJsonFormat); 
    Boolean removeLastCommaForJsonFormat = Boolean.valueOf((sb.length() > 0));
    if (removeLastCommaForJsonFormat.booleanValue())
      sb.setLength(sb.length() - 1); 
    sb.append(getTail());
    return sb.toString();
  }
  
  private synchronized int saveExperience(SaveTo saveTo, String experienceData) {
    if (SaveTo.REQUEST_TO_BUFFER == saveTo && experienceData.length() != 0) {
      this.bufferStoreStartTick = (this.experienceStoreBuffer.length() == 0) ? System.currentTimeMillis() : this.bufferStoreStartTick;
      if (this.experienceStoreBuffer.length() > 0)
        this.experienceStoreBuffer.append("," + System.lineSeparator()); 
      this.experienceStoreBuffer.append(experienceData);
      this.experienceStoreCount++;
      return this.experienceStoreCount;
    } 
    if (SaveTo.BUFFER_TO_FILE == saveTo && this.experienceStoreBuffer.length() > 0) {
      saveBufferToFile();
      int StoredCount = this.experienceStoreCount;
      this.experienceStoreCount = 0;
      this.experienceStoreBuffer.setLength(0);
      return StoredCount;
    } 
    return 0;
  }
  
  private synchronized Boolean saveBufferToFile() {
    if (false == hasEnoughDiskStorage().booleanValue())
      return Boolean.valueOf(false); 
    Path filePath = Paths.get(this.magicInfoProperties.getUserExperienceStorePath(), new String[] { getStoreFileName() });
    File file = filePath.toFile();
    Boolean needCommaBeforeAppend = Boolean.valueOf(!Files.notExists(filePath, new java.nio.file.LinkOption[0]));
    if (needCommaBeforeAppend.booleanValue())
      needCommaBeforeAppend = Boolean.valueOf((FileUtils.sizeOf(file) > 0L)); 
    StringBuilder bufferToKeepJson = needCommaBeforeAppend.booleanValue() ? new StringBuilder("," + System.lineSeparator()) : new StringBuilder("");
    bufferToKeepJson.append(this.experienceStoreBuffer.toString());
    try {
      FileUtils.writeStringToFile(file, bufferToKeepJson.toString(), StandardCharsets.UTF_8, true);
    } catch (IOException ex) {
      this.LOGGER.error(ex.getMessage());
      return Boolean.valueOf(false);
    } 
    return Boolean.valueOf(true);
  }
  
  public String getStoreFileName() {
    String headName = "KPI.WebAuthor." + getHardwareUniqueId() + "." + this.dateFormat.format(new Date()) + ".";
    String storeFileName = "";
    long recommendedFileSize = 20971520L;
    int enoughTrySize = 10000;
    for (int i = 0; i < enoughTrySize; i++) {
      storeFileName = headName + Integer.toString(i) + ".json";
      Path filePath = Paths.get(this.magicInfoProperties.getUserExperienceStorePath(), new String[] { storeFileName });
      if (Files.notExists(filePath, new java.nio.file.LinkOption[0]))
        break; 
      if (FileUtils.sizeOf(filePath.toFile()) < recommendedFileSize)
        break; 
    } 
    return storeFileName;
  }
  
  private String getHardwareUniqueId() {
    if (!this.hardwareUniqueId.isEmpty())
      return this.hardwareUniqueId; 
    ServerSystemInfoData serverSystemInfoData = this.openAPISystemInfoRepository.getServerSystemInfo();
    String responseHardwareUniqueId = serverSystemInfoData.getHwUniqueKey();
    if (null == responseHardwareUniqueId || responseHardwareUniqueId.isEmpty())
      return "unknownHwId"; 
    this.hardwareUniqueId = responseHardwareUniqueId;
    return this.hardwareUniqueId;
  }
  
  private Boolean hasEnoughDiskStorage() {
    Path path = Paths.get(this.magicInfoProperties.getUserExperienceStorePath(), new String[0]);
    long recommendedFreeSpace = 1073741824L;
    Path root = path.getRoot();
    long usableSpace = root.toFile().getUsableSpace();
    if (usableSpace <= recommendedFreeSpace) {
      this.LOGGER.warn("Usable Space: " + usableSpace + " < Recommended: " + recommendedFreeSpace + ", skip storing UserExperience");
      return Boolean.valueOf(false);
    } 
    return Boolean.valueOf(true);
  }
  
  private String getHeader() {
    StringBuilder sb = new StringBuilder("{");
    sb.append("\"").append("time").append("\":\"").append(this.dateTimeFormat.format(new Date())).append("\",");
    sb.append("\"").append("type").append("\":\"").append("WA").append("\",");
    sb.append("\"").append("uid").append("\":\"").append(getHardwareUniqueId()).append("\",");
    sb.append("\"").append("category").append("\":[");
    return sb.toString();
  }
  
  private String getTail() {
    return "]}";
  }
  
  private void sleep(int delay) {
    try {
      Thread.sleep(delay);
    } catch (InterruptedException ex) {
      this.LOGGER.warn("UserExperience(), sleep canceled: " + ex.getMessage());
    } 
  }
}
