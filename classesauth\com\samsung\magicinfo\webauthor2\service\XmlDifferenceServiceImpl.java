package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.service.XmlDifferenceService;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;
import org.xmlunit.builder.DiffBuilder;
import org.xmlunit.diff.DefaultNodeMatcher;
import org.xmlunit.diff.Diff;
import org.xmlunit.diff.Difference;
import org.xmlunit.diff.ElementSelector;
import org.xmlunit.diff.ElementSelectors;
import org.xmlunit.diff.NodeMatcher;

@Service
public class XmlDifferenceServiceImpl implements XmlDifferenceService {
  private static final int EPSILON = 2;
  
  public List<String> getDifferences(String originalXml, String testXml) {
    List<String> differences = new ArrayList<>();
    Diff diff = DiffBuilder.compare(originalXml).withTest(testXml).ignoreWhitespace().withNodeMatcher((NodeMatcher)new DefaultNodeMatcher(new ElementSelector[] { ElementSelectors.byNameAndAllAttributes })).checkForSimilar().build();
    for (Difference d : diff.getDifferences()) {
      Object testValue = d.getComparison().getTestDetails().getValue();
      Object controlValue = d.getComparison().getControlDetails().getValue();
      if (testValue != null && controlValue != null) {
        try {
          int control = Integer.parseInt(controlValue.toString());
          int test = Integer.parseInt(testValue.toString());
          if (Math.abs(control - test) > 2)
            differences.add(d.toString()); 
        } catch (NumberFormatException ex) {
          differences.add(d.toString());
        } 
        continue;
      } 
      differences.add(d.toString());
    } 
    return differences;
  }
}
