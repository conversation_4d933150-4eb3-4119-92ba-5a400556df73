package classes.com.samsung.magicinfo.webauthor2.config;

import com.samsung.magicinfo.webauthor2.config.AppConfig;
import com.samsung.magicinfo.webauthor2.config.MvcConfig;
import com.samsung.magicinfo.webauthor2.util.SessionListener;
import java.nio.charset.StandardCharsets;
import java.util.EventListener;
import javax.servlet.Filter;
import javax.servlet.ServletContext;
import javax.servlet.ServletException;
import org.springframework.web.filter.CharacterEncodingFilter;
import org.springframework.web.servlet.support.AbstractAnnotationConfigDispatcherServletInitializer;

public class WebConfig extends AbstractAnnotationConfigDispatcherServletInitializer {
  protected Class<?>[] getRootConfigClasses() {
    return new Class[] { AppConfig.class };
  }
  
  protected Class<?>[] getServletConfigClasses() {
    return new Class[] { MvcConfig.class };
  }
  
  protected String[] getServletMappings() {
    return new String[] { "/" };
  }
  
  protected Filter[] getServletFilters() {
    CharacterEncodingFilter characterEncodingFilter = new CharacterEncodingFilter(StandardCharsets.UTF_8.toString(), true);
    return new Filter[] { (Filter)characterEncodingFilter };
  }
  
  public void onStartup(ServletContext servletContext) throws ServletException {
    super.onStartup(servletContext);
    servletContext.addListener((EventListener)new SessionListener());
  }
}
