package classes.com.samsung.magicinfo.webauthor2.webapi.assembler;

import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.model.VWLCanvasContent;
import com.samsung.magicinfo.webauthor2.model.VWLContent;
import com.samsung.magicinfo.webauthor2.webapi.controller.DataLinkQueryController;
import com.samsung.magicinfo.webauthor2.webapi.resource.VWLContentResource;
import org.springframework.hateoas.ResourceAssembler;
import org.springframework.hateoas.ResourceSupport;
import org.springframework.hateoas.mvc.BasicLinkBuilder;
import org.springframework.hateoas.mvc.ControllerLinkBuilder;
import org.springframework.stereotype.Component;

@Component
public class VWLContentResourceAssembler implements ResourceAssembler<VWLContent, VWLContentResource> {
  public VWLContentResource toResource(VWLContent vwlContent) {
    VWLContentResource vwlContentResource = new VWLContentResource(vwlContent, new org.springframework.hateoas.Link[0]);
    vwlContentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping()
        .slash("content"))
        .slash(vwlContent.getThumbnailId()))
        .slash(vwlContent.getThumbnailName()))
        .withRel("thumbnailUrl"));
    vwlContentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping()
        .slash("content"))
        .slash(vwlContent.getFileId()))
        .slash(vwlContent.getFileName()))
        .withRel("mainUrl"));
    int count = 1;
    for (VWLCanvasContent canvasContent : vwlContent.getCanvasContents()) {
      vwlContentResource.add(((BasicLinkBuilder)((BasicLinkBuilder)((BasicLinkBuilder)BasicLinkBuilder.linkToCurrentMapping()
          .slash("content"))
          .slash(canvasContent.getFileId()))
          .slash(canvasContent.getFileName()))
          .withRel("canvasContent_" + count));
      count++;
    } 
    if (vwlContent.getVwlInfo().getType() == MediaType.DLK)
      vwlContentResource.add(ControllerLinkBuilder.linkTo(((DataLinkQueryController)ControllerLinkBuilder.methodOn(DataLinkQueryController.class, new Object[0]))
            .getDataLinkDescriptor(vwlContent.getId())).withRel("dataLinkModel")); 
    return vwlContentResource;
  }
}
