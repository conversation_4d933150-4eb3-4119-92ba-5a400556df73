package classes.com.samsung.magicinfo.webauthor2.service.datalink;

import com.samsung.magicinfo.webauthor2.model.datalink.ConvertTable;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.DLKDataType;
import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.model.datalink.DynamicDLKData;
import com.samsung.magicinfo.webauthor2.model.datalink.Element;
import com.samsung.magicinfo.webauthor2.model.datalink.Page;
import com.samsung.magicinfo.webauthor2.model.datalink.SplitGroup;
import com.samsung.magicinfo.webauthor2.model.datalink.SyncGroup;

public abstract class ConvertTableSearchAlg<T> {
  public void searchConvertTable(DataLinkDescriptor dataLinkDescriptor) {
    for (int pageIndex = 0; pageIndex < dataLinkDescriptor.getPages().size(); pageIndex++) {
      int pageNumb = pageIndex;
      Page page = dataLinkDescriptor.getPages().get(pageIndex);
      if (page.getSyncGroups() != null && page.getSyncGroups().size() > 0)
        for (SyncGroup syncGroup : page.getSyncGroups()) {
          for (SplitGroup splitGroup : syncGroup.getSplitGroups()) {
            int splitGroupId = splitGroup.getId();
            String splitGroupName = splitGroup.getName();
            for (Element element : splitGroup.getElements()) {
              String elementName = element.getName();
              if (element.getDataList() != null && !element.getDataList().isEmpty())
                for (int i = 0; i < element.getDataList().size(); i++) {
                  int dataIndex = i + 1;
                  DLKData dlkData = element.getDataList().get(i);
                  if (dlkData.getType() == DLKDataType.Dynamic) {
                    DynamicDLKData dynamicDLKData = (DynamicDLKData)dlkData;
                    ConvertTable convertTable = dynamicDLKData.getConvertTable();
                    if (convertTable != null) {
                      String convertTableName = convertTable.getName();
                      actionOnConvertTable(convertTable, pageNumb, elementName, splitGroupId, splitGroupName, dataIndex, convertTableName);
                    } 
                  } 
                }  
            } 
          } 
        }  
    } 
  }
  
  protected abstract void actionOnConvertTable(ConvertTable paramConvertTable, int paramInt1, String paramString1, int paramInt2, String paramString2, int paramInt3, String paramString3);
  
  public abstract T getResult();
}
