package classes.com.samsung.magicinfo.webauthor2.repository.model.tag;

import com.samsung.magicinfo.webauthor2.repository.model.tag.TagResultListData;
import java.io.Serializable;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;

@XmlRootElement(name = "response")
public class TagResponseData implements Serializable {
  private static final long serialVersionUID = -3159926740292038104L;
  
  @XmlAttribute
  private String code;
  
  @XmlElement
  private String errorMessage;
  
  @XmlElement
  private TagResultListData responseClass;
  
  public String getCode() {
    return this.code;
  }
  
  public String getErrorMessage() {
    return this.errorMessage;
  }
  
  public TagResultListData getResponseClass() {
    return this.responseClass;
  }
}
