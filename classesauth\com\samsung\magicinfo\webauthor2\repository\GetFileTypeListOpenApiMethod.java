package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseFileTypesData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListFileTypesData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetFileTypeListOpenApiMethod extends OpenApiMethod<List<String>, ResponseFileTypesData> {
  private String mediaType;
  
  private String deviceType;
  
  private String token;
  
  public GetFileTypeListOpenApiMethod(String mediaType, String deviceType, RestTemplate restTemplate, String token) {
    super(restTemplate);
    this.mediaType = mediaType;
    this.deviceType = deviceType;
    this.token = token;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getFileTypeList";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("mediaType", this.mediaType);
    vars.put("deviceType", this.deviceType);
    return vars;
  }
  
  Class<ResponseFileTypesData> getResponseClass() {
    return ResponseFileTypesData.class;
  }
  
  List<String> convertResponseData(ResponseFileTypesData responseFileTypesData) {
    List<String> list;
    ResultListFileTypesData resultList = responseFileTypesData.getResponseClass();
    if (resultList != null && resultList.getResultList() != null) {
      list = resultList.getResultList();
    } else {
      list = new ArrayList<>();
    } 
    return list;
  }
}
