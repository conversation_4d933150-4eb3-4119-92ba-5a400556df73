package classes.com.samsung.magicinfo.webauthor2.service.transferfile;

import com.samsung.magicinfo.webauthor2.service.transferfile.TransferFileXmlResponseFactory;
import com.samsung.magicinfo.webauthor2.xml.transferfile.response.TransferFilesResponseType;
import javax.inject.Inject;
import javax.xml.transform.Source;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringSource;

@Service
public class TransferFileXmlResponseFactoryImpl implements TransferFileXmlResponseFactory {
  private final Jaxb2Marshaller jaxb2MarshallerForResponseTransferFile;
  
  @Inject
  public TransferFileXmlResponseFactoryImpl(Jaxb2Marshaller jaxb2MarshallerForResponseTransferFile) {
    this.jaxb2MarshallerForResponseTransferFile = jaxb2MarshallerForResponseTransferFile;
  }
  
  public TransferFilesResponseType unmarshal(String csdResponseXml) {
    StringSource stringSource = new StringSource(csdResponseXml);
    TransferFilesResponseType transferFilesResponseType = (TransferFilesResponseType)this.jaxb2MarshallerForResponseTransferFile.unmarshal((Source)stringSource);
    return transferFilesResponseType;
  }
}
