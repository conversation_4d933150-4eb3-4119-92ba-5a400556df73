package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MediaType;
import com.samsung.magicinfo.webauthor2.repository.model.FileInfoData;
import java.util.List;

public interface OpenAPIFileRepository {
  List<String> getFileTypeList(MediaType paramMediaType, DeviceType paramDeviceType);
  
  FileInfoData getFileInfo(String paramString);
  
  List<String> getContentFileList(String paramString);
}
