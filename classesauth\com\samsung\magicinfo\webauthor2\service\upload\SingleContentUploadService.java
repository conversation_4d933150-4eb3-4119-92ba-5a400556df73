package classes.com.samsung.magicinfo.webauthor2.service.upload;

import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import java.io.IOException;
import org.springframework.web.multipart.MultipartFile;

public interface SingleContentUploadService {
  String upload(MultipartFile paramMultipartFile, DeviceType paramDeviceType) throws FileItemValidationException, IOException;
  
  String upload(String paramString, DeviceType paramDeviceType) throws IOException;
}
