package classes.com.samsung.magicinfo.webauthor2.util;

import com.google.common.base.Joiner;
import com.samsung.magicinfo.webauthor2.exception.service.CannotFindDataLinkServerException;
import com.samsung.magicinfo.webauthor2.model.DataLinkServer;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.MagicInfoUserRole;
import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableListType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.context.annotation.SessionScope;

@Component
@SessionScope
public class UserData implements Serializable {
  private static final long serialVersionUID = 7501894884287345553L;
  
  private String userId;
  
  private String token;
  
  private Locale locale;
  
  private String sessionTimestamp;
  
  private String language;
  
  private String sessionMisOpenApiVersion = "";
  
  private int sessionMisOpenApiVersionMajor = 0;
  
  private int sessionMisOpenApiVersionMinor = 0;
  
  private String startContentId;
  
  private Set<DeviceType> supportedDeviceTypes;
  
  private Map<String, DataLinkServer> dataLinkServers = new HashMap<>();
  
  private ConvertTableListType dlkConvertTableList;
  
  private MagicInfoUserRole userRole;
  
  public UserData() {
    invalidate();
  }
  
  public synchronized boolean isAnonymous() {
    return (StringUtils.isEmpty(this.userId) || StringUtils.isEmpty(this.token));
  }
  
  public synchronized void setCredentials(String token) {
    this.token = token;
  }
  
  public synchronized void setCredentials(String userId, String token) {
    this.userId = userId;
    this.token = token;
  }
  
  public synchronized void invalidate() {
    this.userId = "";
    this.token = "";
    this.locale = null;
    this.language = "";
    this.sessionTimestamp = Long.toString((new Date()).getTime());
  }
  
  public synchronized String getUserId() {
    return this.userId;
  }
  
  public synchronized String getToken() {
    return this.token;
  }
  
  public synchronized Locale getLocale() {
    return this.locale;
  }
  
  public synchronized void setLocale(Locale locale) {
    this.locale = locale;
  }
  
  public synchronized String getSessionTimestamp() {
    return this.sessionTimestamp;
  }
  
  public String getWorkspaceFolderName() {
    return Joiner.on("_").join(getUserId(), getSessionTimestamp(), new Object[0]);
  }
  
  public Set<DeviceType> getSupportedDeviceTypes() {
    return this.supportedDeviceTypes;
  }
  
  public void setSupportedDeviceTypes(Set<DeviceType> supportedDeviceTypes) {
    this.supportedDeviceTypes = supportedDeviceTypes;
  }
  
  public void updateDataLinkServers(Map<String, DataLinkServer> dataLinkServers) {
    this.dataLinkServers = dataLinkServers;
  }
  
  public List<DataLinkServer> getDataLinkServers() {
    return new ArrayList<>(this.dataLinkServers.values());
  }
  
  public DataLinkServer getDataLinkServer(String serverName) {
    DataLinkServer dataLinkServer = this.dataLinkServers.get(serverName);
    if (dataLinkServer == null)
      throw new CannotFindDataLinkServerException(serverName); 
    return dataLinkServer;
  }
  
  public synchronized String getLanguage() {
    return this.language;
  }
  
  public synchronized void setLanguage(String language) {
    this.language = language;
  }
  
  public String getStartContentId() {
    return this.startContentId;
  }
  
  public void setStartContentId(String startContentId) {
    this.startContentId = startContentId;
  }
  
  public MagicInfoUserRole getUserRole() {
    return this.userRole;
  }
  
  public void setUserRole(MagicInfoUserRole userRole) {
    this.userRole = userRole;
  }
  
  public String toString() {
    return (new ToStringBuilder(this))
      .append("userId", this.userId)
      .append("token", this.token)
      .append("language", this.language)
      .toString();
  }
  
  public void putDlkConvertTableList(ConvertTableListType dlkConvertTableList) {
    this.dlkConvertTableList = dlkConvertTableList;
  }
  
  public ConvertTableListType popDlkConvertTableList() {
    ConvertTableListType tmpConvertTableList = this.dlkConvertTableList;
    this.dlkConvertTableList = null;
    return tmpConvertTableList;
  }
  
  public void setSessionMisOpenApiVersion(String openapiVersion) {
    this.sessionMisOpenApiVersion = openapiVersion;
    this.sessionMisOpenApiVersion = this.sessionMisOpenApiVersion.replaceAll(" ", "");
    this.sessionMisOpenApiVersion = this.sessionMisOpenApiVersion.replaceAll("[^0-9.]", "0");
    this.sessionMisOpenApiVersionMajor = (int)Double.parseDouble(this.sessionMisOpenApiVersion);
  }
  
  public Boolean isSessionMisOpenApiVersionHigherOrEqualThan(int requiredMajorVersion) {
    if (this.sessionMisOpenApiVersionMajor == 0)
      return Boolean.valueOf(false); 
    return Boolean.valueOf((this.sessionMisOpenApiVersionMajor >= requiredMajorVersion));
  }
  
  public Boolean isUrlAuthNotAllowedToThisMisServletSession() {
    int requiredMajorVersion = 201000;
    return Boolean.valueOf((this.sessionMisOpenApiVersionMajor >= requiredMajorVersion));
  }
}
