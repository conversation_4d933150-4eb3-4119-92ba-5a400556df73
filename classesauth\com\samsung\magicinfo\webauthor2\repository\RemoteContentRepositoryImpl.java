package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.MagicInfoRemoteContentException;
import com.samsung.magicinfo.webauthor2.repository.RemoteContentRepository;
import com.samsung.magicinfo.webauthor2.util.OperatingSystem;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

@Repository
public class RemoteContentRepositoryImpl implements RemoteContentRepository {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.repository.RemoteContentRepositoryImpl.class);
  
  private static final String LIB_WIN32_EXECUTABLE = "ttfpatch.exe";
  
  private static final String LIB_WIN64_EXECUTABLE = "embed.exe";
  
  private static final String LIB_LINUX_EXECUTABLE = "embed";
  
  private static final String LIB_SRC_LOCATION = "ttfpatch/";
  
  private static final String LIB_DST_LOCATION = "fonts/";
  
  private RestTemplate restTemplate;
  
  private ServletContext servletContext;
  
  private final UserData userData;
  
  @Autowired
  public RemoteContentRepositoryImpl(RestTemplate restTemplate, ServletContext servletContext, UserData userData) {
    this.restTemplate = restTemplate;
    this.servletContext = servletContext;
    this.userData = userData;
  }
  
  public String getXmlFileContents(String fileId, String fileName) throws MagicInfoRemoteContentException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/FileLoader").build().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    byte[] requestBody = ("CONTENTS_HOME/" + fileId + "/" + fileName).getBytes(StandardCharsets.UTF_8);
    HttpEntity<byte[]> request = new HttpEntity(requestBody, (MultiValueMap)headers);
    try {
      ResponseEntity<String> response = this.restTemplate.exchange(uri, HttpMethod.POST, request, String.class);
      return (String)response.getBody();
    } catch (UnknownHttpStatusCodeException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public Path getContentFileFromMagicInfoServer(Path workspaceFolder, String fileId, String fileName) throws IOException, MagicInfoRemoteContentException {
    String relativePath = fileId;
    return getContentFileFromMagicInfoServer(workspaceFolder, relativePath, fileId, fileName);
  }
  
  public Path getContentFileFromMagicInfoServer(Path workspaceFolder, String relativePath, String fileId, String fileName) throws IOException, MagicInfoRemoteContentException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/FileLoader").build().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    byte[] requestBody = ("CONTENTS_HOME/" + fileId + "/" + fileName).getBytes(StandardCharsets.UTF_8);
    HttpEntity<byte[]> request = new HttpEntity(requestBody, (MultiValueMap)headers);
    try {
      logger.debug("REQUEST: " + request.toString());
      byte[] content = (byte[])this.restTemplate.postForObject(uri, request, byte[].class);
      Path downloadedFilePath = workspaceFolder.resolve(relativePath).resolve(fileName);
      FileUtils.deleteQuietly(downloadedFilePath.toFile());
      Path parent = downloadedFilePath.getParent();
      if (parent != null && Files.notExists(parent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(parent, (FileAttribute<?>[])new FileAttribute[0]); 
      try (OutputStream outputStream = Files.newOutputStream(downloadedFilePath, new java.nio.file.OpenOption[0])) {
        IOUtils.write(content, outputStream);
      } 
      return downloadedFilePath;
    } catch (UnknownHttpStatusCodeException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public Path getFontFileFromMagicInfoServer(String fileId, String fileName) throws IOException, MagicInfoRemoteContentException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/FileLoader").build().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    byte[] requestBody = ("CONTENTS_HOME/" + fileId + "/" + fileName).getBytes(StandardCharsets.UTF_8);
    HttpEntity<byte[]> request = new HttpEntity(requestBody, (MultiValueMap)headers);
    try {
      logger.debug("REQUEST: " + request.toString());
      byte[] content = (byte[])this.restTemplate.postForObject(uri, request, byte[].class);
      String fontsDirectory = this.servletContext.getRealPath("fonts");
      Path downloadedFilePath = Paths.get(fontsDirectory, new String[] { fileName });
      FileUtils.deleteQuietly(downloadedFilePath.toFile());
      Path parent = downloadedFilePath.getParent();
      if (parent != null && Files.notExists(parent, new java.nio.file.LinkOption[0]))
        Files.createDirectories(parent, (FileAttribute<?>[])new FileAttribute[0]); 
      try (OutputStream outputStream = Files.newOutputStream(downloadedFilePath, new java.nio.file.OpenOption[0])) {
        IOUtils.write(content, outputStream);
        outputStream.close();
      } 
      embedTtf(fileName);
      return downloadedFilePath;
    } catch (URISyntaxException|UnknownHttpStatusCodeException|InterruptedException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  public byte[] getContentFileFromMagicInfoServer(String fileId, String fileName) throws MagicInfoRemoteContentException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/FileLoader").build().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    byte[] requestBody = ("CONTENTS_HOME/" + fileId + "/" + fileName).getBytes(StandardCharsets.UTF_8);
    HttpEntity<byte[]> request = new HttpEntity(requestBody, (MultiValueMap)headers);
    try {
      logger.debug("REQUEST: " + request.toString());
      return (byte[])this.restTemplate.postForObject(uri, request, byte[].class);
    } catch (UnknownHttpStatusCodeException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } catch (ResourceAccessException ex) {
      logger.warn("ResourceAccessException Exception from Server (Can Not Access the file),  fileId: " + fileId + ", fileName: " + fileName);
      return new byte[0];
    } 
  }
  
  public String getVwlContent(String fileId, String fileName) throws MagicInfoRemoteContentException {
    URI uri = UriComponentsBuilder.newInstance().path("/servlet/FileLoader").build().toUri();
    HttpHeaders headers = new HttpHeaders();
    headers.add("userId", this.userData.getUserId());
    headers.add("token", this.userData.getToken());
    headers.setAcceptCharset(Collections.singletonList(StandardCharsets.UTF_8));
    headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
    byte[] vwtRequestBody = ("vwt/" + fileId + "/" + fileName).getBytes(StandardCharsets.UTF_8);
    HttpEntity<byte[]> request = new HttpEntity(vwtRequestBody, (MultiValueMap)headers);
    try {
      logger.debug("REQUEST: " + request.toString());
      return (String)this.restTemplate.postForObject(uri, request, String.class);
    } catch (UnknownHttpStatusCodeException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } 
  }
  
  private static void copyFile(File oldLocation, File newLocation) throws IOException {
    if (oldLocation.exists()) {
      FileInputStream input = null;
      FileOutputStream output = null;
      BufferedInputStream reader = null;
      BufferedOutputStream writer = null;
      try {
        input = new FileInputStream(oldLocation);
        output = new FileOutputStream(newLocation, false);
        reader = new BufferedInputStream(input);
        writer = new BufferedOutputStream(output);
        byte[] buff = new byte[8192];
        int numChars;
        while ((numChars = reader.read(buff, 0, buff.length)) != -1)
          writer.write(buff, 0, numChars); 
      } catch (IOException ex) {
        throw new IOException("IOException when transferring " + oldLocation.getPath() + " to " + newLocation.getPath());
      } finally {
        if (null != reader)
          reader.close(); 
        if (null != input)
          input.close(); 
        if (null != writer)
          writer.close(); 
        if (null != output)
          output.close(); 
      } 
    } else {
      throw new IOException("Old location does not exist when transferring " + oldLocation.getPath() + " to " + newLocation.getPath());
    } 
  }
  
  public String embedTtf(String fileName) throws URISyntaxException, InterruptedException {
    Process process = null;
    ProcessBuilder pb = null;
    int result = -1;
    try {
      Path srcPath = getSrcLibraryFilePath();
      File srcEmbed = srcPath.toFile();
      Path dstPath = getDstLibraryFilePath();
      File dstEmbed = dstPath.toFile();
      if (!dstEmbed.isFile())
        copyFile(srcEmbed, dstEmbed); 
      List<String> params = new ArrayList<>();
      String fontsDirectory = this.servletContext.getRealPath("fonts");
      params.add(dstPath.toString());
      params.add(fileName);
      pb = new ProcessBuilder(params);
      pb.directory(new File(fontsDirectory));
      process = pb.start();
      result = process.waitFor();
    } catch (IOException ex) {
      throw new MagicInfoRemoteContentException(ex.getMessage());
    } finally {
      if (process != null)
        process.destroy(); 
    } 
    return fileName;
  }
  
  private Path getSrcLibraryFilePath() throws URISyntaxException {
    ClassLoader cl = getClass().getClassLoader();
    if (OperatingSystem.isWindows32())
      return Paths.get(cl.getResource("ttfpatch/ttfpatch.exe").toURI()); 
    if (OperatingSystem.isWindows64())
      return Paths.get(cl.getResource("ttfpatch/embed.exe").toURI()); 
    if (OperatingSystem.isLinux())
      return Paths.get(cl.getResource("ttfpatch/embed").toURI()); 
    throw new UnsupportedOperationException("converting fonts only supported on Windows and Linux operating systems");
  }
  
  private Path getDstLibraryFilePath() {
    String fontsDir = this.servletContext.getRealPath("fonts");
    if (OperatingSystem.isWindows32())
      return Paths.get(fontsDir, new String[] { "ttfpatch.exe" }); 
    if (OperatingSystem.isWindows64())
      return Paths.get(fontsDir, new String[] { "embed.exe" }); 
    if (OperatingSystem.isLinux())
      return Paths.get(fontsDir, new String[] { "embed" }); 
    throw new UnsupportedOperationException("converting fonts only supported on Windows and Linux operating systems");
  }
}
