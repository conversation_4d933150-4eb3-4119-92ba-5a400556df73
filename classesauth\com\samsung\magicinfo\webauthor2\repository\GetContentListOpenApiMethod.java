package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.ResponseData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import com.samsung.magicinfo.webauthor2.util.JaxbUtil;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetContentListOpenApiMethod extends OpenApiMethod<ResultListData, ResponseData> {
  private final String userId;
  
  private final String token;
  
  private final ContentSearchCriteria contentSearchCriteria;
  
  private final DeviceType deviceType;
  
  private final JaxbUtil jaxbUtil;
  
  public GetContentListOpenApiMethod(RestTemplate restTemplate, String userId, String token, DeviceType playerType, ContentSearchCriteria contentSearchCriteria, JaxbUtil jaxbUtil) {
    super(restTemplate);
    this.userId = userId;
    this.token = token;
    this.contentSearchCriteria = contentSearchCriteria;
    this.deviceType = playerType;
    this.jaxbUtil = jaxbUtil;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "getContentList";
  }
  
  protected Map<String, String> getRequestParams() {
    String condition = this.jaxbUtil.searchCriteria(this.contentSearchCriteria);
    Map<String, String> vars = new HashMap<>();
    vars.put("userId", this.userId);
    vars.put("token", this.token);
    vars.put("condition", condition);
    vars.put("deviceType", this.deviceType.toString());
    return vars;
  }
  
  Class<ResponseData> getResponseClass() {
    return ResponseData.class;
  }
  
  ResultListData convertResponseData(ResponseData responseData) {
    return responseData.getResponseClass();
  }
}
