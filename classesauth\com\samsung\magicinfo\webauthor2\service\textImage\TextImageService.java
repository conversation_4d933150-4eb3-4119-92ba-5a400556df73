package classes.com.samsung.magicinfo.webauthor2.service.textImage;

import com.samsung.magicinfo.webauthor2.model.svg.TextImageDescriptor;
import java.nio.file.Path;
import java.util.List;

public interface TextImageService {
  List<String> transcode(List<TextImageDescriptor> paramList);
  
  String transcode(TextImageDescriptor paramTextImageDescriptor);
  
  String transcodeFontHtml(TextImageDescriptor paramTextImageDescriptor);
  
  String transcodeFontImage(TextImageDescriptor paramTextImageDescriptor);
  
  byte[] readPngFile(String paramString);
  
  Path getPngFilePath(String paramString);
}
