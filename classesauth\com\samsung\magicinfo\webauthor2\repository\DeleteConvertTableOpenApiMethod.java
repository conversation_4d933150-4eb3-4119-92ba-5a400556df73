package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.CannotDeleteConvertTableException;
import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.OpenAPIResponseData;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class DeleteConvertTableOpenApiMethod extends OpenApiMethod<String, OpenAPIResponseData> {
  private final String convertTableName;
  
  private final String token;
  
  public DeleteConvertTableOpenApiMethod(RestTemplate restTemplate, String token, String convertTableName) {
    super(restTemplate);
    this.token = token;
    this.convertTableName = convertTableName;
  }
  
  protected String getOpenApiClassName() {
    return "CommonContentService";
  }
  
  protected String getOpenApiMethodName() {
    return "deleteConvertTable";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("tableName", this.convertTableName);
    vars.put("token", this.token);
    return vars;
  }
  
  Class<OpenAPIResponseData> getResponseClass() {
    return OpenAPIResponseData.class;
  }
  
  String convertResponseData(OpenAPIResponseData responseData) {
    if (responseData.getErrorMessage() != null)
      throw new CannotDeleteConvertTableException(responseData.getCode(), responseData.getErrorMessage()); 
    return responseData.getResponseClass();
  }
}
