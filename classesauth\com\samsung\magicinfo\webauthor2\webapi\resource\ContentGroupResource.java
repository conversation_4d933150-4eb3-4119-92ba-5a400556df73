package classes.com.samsung.magicinfo.webauthor2.webapi.resource;

import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import java.io.Serializable;
import org.springframework.hateoas.Link;
import org.springframework.hateoas.Resource;

public class ContentGroupResource extends Resource<ContentGroup> implements Serializable {
  private static final long serialVersionUID = 1L;
  
  public ContentGroupResource(ContentGroup contentGroup, Iterable<Link> links) {
    super(contentGroup, links);
  }
  
  public ContentGroupResource(ContentGroup contentGroup, Link... links) {
    super(contentGroup, links);
  }
}
