package classes.com.samsung.magicinfo.webauthor2.config;

import java.util.ArrayList;
import java.util.List;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.ComponentScan.Filter;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.FilterType;
import org.springframework.data.web.config.EnableSpringDataWebSupport;
import org.springframework.hateoas.MediaTypes;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.accept.ContentNegotiationManager;
import org.springframework.web.multipart.commons.CommonsMultipartResolver;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.PathMatchConfigurer;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurerAdapter;
import org.springframework.web.servlet.i18n.SessionLocaleResolver;
import org.springframework.web.servlet.view.ContentNegotiatingViewResolver;
import org.springframework.web.servlet.view.InternalResourceViewResolver;
import org.springframework.web.servlet.view.json.MappingJackson2JsonView;

@Configuration
@EnableWebMvc
@EnableSpringDataWebSupport
@ComponentScan(basePackages = {"com.samsung.magicinfo"}, excludeFilters = {@Filter(type = FilterType.ANNOTATION, value = {Configuration.class})})
public class MvcConfig extends WebMvcConfigurerAdapter {
  public static final String CONTENTS_RESOURCE_MAPPED_NAME = "content";
  
  public static final String VWT_RESOURCE_MAPPED_NAME = "vwt";
  
  public void addResourceHandlers(ResourceHandlerRegistry registry) {
    super.addResourceHandlers(registry);
    registry.addResourceHandler(new String[] { "/assets/**" }).addResourceLocations(new String[] { "/assets/" });
    registry.addResourceHandler(new String[] { "/preview/**" }).addResourceLocations(new String[] { "/preview/" });
    registry.addResourceHandler(new String[] { "/import/**" }).addResourceLocations(new String[] { "/import/" });
    registry.addResourceHandler(new String[] { "/images/**" }).addResourceLocations(new String[] { "/images/" });
    registry.addResourceHandler(new String[] { "/fonts/**" }).addResourceLocations(new String[] { "/fonts/" });
    registry.addResourceHandler(new String[] { "/i18n/**" }).addResourceLocations(new String[] { "/i18n/" });
    registry.addResourceHandler(new String[] { "/src/**" }).addResourceLocations(new String[] { "/src/" });
    registry.addResourceHandler(new String[] { "/src.layout/**" }).addResourceLocations(new String[] { "/src.layout/" });
    registry.addResourceHandler(new String[] { "/src.led/**" }).addResourceLocations(new String[] { "/src.led/" });
    registry.addResourceHandler(new String[] { "/src.vw/**" }).addResourceLocations(new String[] { "/src.vw/" });
    registry.addResourceHandler(new String[] { "/index.jsp" }).addResourceLocations(new String[] { "/" });
  }
  
  public void addInterceptors(InterceptorRegistry registry) {
    super.addInterceptors(registry);
  }
  
  public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
    configurer.defaultContentType(MediaTypes.HAL_JSON);
  }
  
  public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
    converters.add(new MappingJackson2HttpMessageConverter());
    converters.add(new ResourceHttpMessageConverter());
    converters.add(new StringHttpMessageConverter());
    converters.add(new ByteArrayHttpMessageConverter());
  }
  
  public void configurePathMatch(PathMatchConfigurer configurer) {
    configurer.setUseSuffixPatternMatch(Boolean.valueOf(false));
  }
  
  @Bean
  public InternalResourceViewResolver internalResourceViewResolver() {
    InternalResourceViewResolver resolver = new InternalResourceViewResolver();
    resolver.setPrefix("/WEB-INF/views/");
    resolver.setSuffix(".jsp");
    resolver.setOrder(2);
    return resolver;
  }
  
  @Bean
  public SessionLocaleResolver localeResolver() {
    return new SessionLocaleResolver();
  }
  
  @Bean
  public ContentNegotiatingViewResolver contentNegotiatingViewResolver(ContentNegotiationManager contentNegotiationManager) {
    ContentNegotiatingViewResolver contentNegotiatingViewResolver = new ContentNegotiatingViewResolver();
    contentNegotiatingViewResolver.setOrder(1);
    contentNegotiatingViewResolver.setContentNegotiationManager(contentNegotiationManager);
    List<View> views = new ArrayList<>();
    views.add(new MappingJackson2JsonView());
    contentNegotiatingViewResolver.setDefaultViews(views);
    return contentNegotiatingViewResolver;
  }
  
  @Bean
  public CommonsMultipartResolver multipartResolver() {
    return new CommonsMultipartResolver();
  }
}
