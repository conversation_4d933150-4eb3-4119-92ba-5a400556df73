package classes.com.samsung.magicinfo.webauthor2.xml.datalink;

import com.samsung.magicinfo.webauthor2.xml.adapters.AdapterXmlBooleanType;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ErrorType", propOrder = {"keepLastValue"})
public class ErrorType {
  @XmlElement(name = "KeepLastValue", required = true)
  @XmlJavaTypeAdapter(AdapterXmlBooleanType.class)
  protected Boolean keepLastValue;
  
  public boolean getKeepLastValue() {
    return this.keepLastValue.booleanValue();
  }
  
  public void setKeepLastValue(boolean value) {
    this.keepLastValue = Boolean.valueOf(value);
  }
}
