package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.FileItemsDescriptor;
import com.samsung.magicinfo.webauthor2.model.NewFileInfo;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.io.IOException;
import java.util.List;
import javax.servlet.http.HttpServletRequest;

public interface ContentSaveService {
  List<MediaSource> getFilledMediaSources(FileItemsDescriptor paramFileItemsDescriptor, HttpServletRequest paramHttpServletRequest);
  
  List<MediaSource> getUpdatedMediaSources(List<MediaSource> paramList);
  
  void saveProjectProperties(FileItemsDescriptor paramFileItemsDescriptor);
  
  MediaSource getMediaSourceFromNewFile(NewFileInfo paramNewFileInfo) throws IOException;
  
  void addThumbnailMediaSource();
}
