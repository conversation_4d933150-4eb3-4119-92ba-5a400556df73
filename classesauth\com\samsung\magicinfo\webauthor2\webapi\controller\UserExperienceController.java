package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.service.UserExperienceService;
import java.io.IOException;
import java.util.List;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/experience"})
public class UserExperienceController {
  private MagicInfoProperties magicInfoProperties;
  
  private UserExperienceService userExperienceService;
  
  private final Logger LOGGER = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.UserExperienceController.class);
  
  @Inject
  public UserExperienceController(UserExperienceService userExperienceService, MagicInfoProperties magicInfoProperties) {
    this.userExperienceService = userExperienceService;
    this.magicInfoProperties = magicInfoProperties;
  }
  
  @PostMapping({"/add"})
  public HttpEntity<Integer> add(@RequestBody List<String> experiences) throws IOException {
    if (experiences.isEmpty())
      return (HttpEntity<Integer>)ResponseEntity.ok(Integer.valueOf(0)); 
    int experienceBufferCount = this.userExperienceService.addToBuffer(experiences);
    this.userExperienceService.saveBufferToFileNotTooFrequently(experienceBufferCount);
    return (HttpEntity<Integer>)ResponseEntity.ok(Integer.valueOf(experienceBufferCount));
  }
  
  @GetMapping({"/enabled"})
  public HttpEntity<Boolean> isEnabled() {
    Boolean isEnabled = Boolean.valueOf(this.magicInfoProperties.getUserExperienceEnable());
    return (HttpEntity<Boolean>)ResponseEntity.ok(isEnabled);
  }
  
  @ExceptionHandler({IOException.class})
  public HttpEntity<String> ioExceptionHandler(IOException ex) {
    this.LOGGER.info("UserExperienceController, throwed IOException");
    return (HttpEntity<String>)ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(ex.getMessage());
  }
}
