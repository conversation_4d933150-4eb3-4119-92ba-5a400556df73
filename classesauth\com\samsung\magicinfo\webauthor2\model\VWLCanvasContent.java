package classes.com.samsung.magicinfo.webauthor2.model;

public class VWLCanvasContent {
  private final String fileId;
  
  private final String fileName;
  
  public VWLCanvasContent(String fileId, String fileName) {
    this.fileId = fileId;
    this.fileName = fileName;
  }
  
  public String getFileId() {
    return this.fileId;
  }
  
  public String getFileName() {
    return this.fileName;
  }
  
  public boolean equals(Object o) {
    if (this == o)
      return true; 
    if (!(o instanceof com.samsung.magicinfo.webauthor2.model.VWLCanvasContent))
      return false; 
    com.samsung.magicinfo.webauthor2.model.VWLCanvasContent that = (com.samsung.magicinfo.webauthor2.model.VWLCanvasContent)o;
    if (!this.fileId.equals(that.fileId))
      return false; 
    if (!this.fileName.equals(that.fileName))
      return false; 
    return true;
  }
  
  public int hashCode() {
    int result = this.fileId.hashCode();
    result = 31 * result + this.fileName.hashCode();
    return result;
  }
}
