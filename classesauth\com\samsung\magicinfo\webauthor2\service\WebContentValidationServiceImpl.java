package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.exception.service.FileItemValidationException;
import com.samsung.magicinfo.webauthor2.exception.service.WebContentValidationException;
import com.samsung.magicinfo.webauthor2.properties.MagicInfoProperties;
import com.samsung.magicinfo.webauthor2.service.WebContentValidationService;
import com.samsung.magicinfo.webauthor2.util.MultipartFilenameValidator;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.FileAttribute;
import java.util.Date;
import java.util.Enumeration;
import java.util.zip.ZipEntry;
import java.util.zip.ZipFile;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class WebContentValidationServiceImpl implements WebContentValidationService {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.WebContentValidationServiceImpl.class);
  
  private MultipartFilenameValidator filenameValidator;
  
  private UserData userData;
  
  private ServletContext servletContext;
  
  private MagicInfoProperties magicInfoProperties;
  
  @Autowired
  public WebContentValidationServiceImpl(MultipartFilenameValidator filenameValidator, UserData userData, ServletContext servletContext, MagicInfoProperties magicInfoProperties) {
    this.filenameValidator = filenameValidator;
    this.userData = userData;
    this.servletContext = servletContext;
    this.magicInfoProperties = magicInfoProperties;
  }
  
  public void validateWebContentInZip(MultipartFile webContentInZip, String startupPage) throws IOException, WebContentValidationException, FileItemValidationException {
    validateZipFileExtension(webContentInZip);
    this.filenameValidator.validateZipFile(webContentInZip);
    try (ZipFile zipFile = getZipFileFromMultipartFile(webContentInZip)) {
      validateZipFileContents(zipFile, startupPage);
    } 
  }
  
  public void validateWebContentInZip(String zipFilePath, String startupPage) throws IOException, WebContentValidationException, FileItemValidationException {
    Path realFilePath = Paths.get(this.servletContext.getRealPath(zipFilePath), new String[0]);
    try (ZipFile zipFile = new ZipFile(realFilePath.toFile())) {
      validateZipFileContents(zipFile, startupPage);
    } 
  }
  
  private void validateZipFileExtension(MultipartFile webContentInZip) throws FileItemValidationException {
    try {
      this.filenameValidator.validateFileItem(webContentInZip);
    } catch (FileItemValidationException ex) {
      if (ex.getMessage().equals("InvalidFileType") && 
        !FilenameUtils.getExtension(webContentInZip.getOriginalFilename()).equals("zip"))
        throw new FileItemValidationException(ex.getErrorCode(), ex.getMessage()); 
    } 
  }
  
  private ZipFile getZipFileFromMultipartFile(MultipartFile multipartFile) throws IOException {
    Path workingDirectory = Paths.get(this.servletContext.getRealPath("insertContents"), new String[] { this.userData.getUserId(), 
          Long.toString((new Date()).getTime()) });
    if (Files.exists(workingDirectory, new java.nio.file.LinkOption[0]))
      FileUtils.deleteQuietly(workingDirectory.toFile()); 
    Files.createDirectories(workingDirectory, (FileAttribute<?>[])new FileAttribute[0]);
    Path filePath = Paths.get(workingDirectory.toString(), new String[] { multipartFile.getOriginalFilename() });
    File file = filePath.toFile();
    FileUtils.copyInputStreamToFile(multipartFile.getInputStream(), file);
    ZipFile zipFile = new ZipFile(file);
    return zipFile;
  }
  
  private void validateZipFileContents(ZipFile zipFile, String startupPage) throws WebContentValidationException, IOException {
    Boolean policyToVerifyStartupHtmlExists = Boolean.valueOf(false);
    if (policyToVerifyStartupHtmlExists.booleanValue() && 
      !zipContainsIndexHtml(zipFile, startupPage)) {
      zipFile.close();
      throw new WebContentValidationException(500, "Zip file doesn't contain startup page");
    } 
    if (zipContainsExecutables(zipFile)) {
      zipFile.close();
      throw new WebContentValidationException(500, "Zip file contains executable file");
    } 
    if (zipExceedSizeLimit(zipFile)) {
      zipFile.close();
      throw new WebContentValidationException(500, "Zip file size exceeds the limits");
    } 
  }
  
  private boolean zipContainsIndexHtml(ZipFile zipFile, String startupPage) {
    Enumeration<? extends ZipEntry> e = zipFile.entries();
    while (e.hasMoreElements()) {
      String name = ((ZipEntry)e.nextElement()).getName();
      if (name.equalsIgnoreCase(startupPage))
        return true; 
    } 
    return false;
  }
  
  private boolean zipContainsExecutables(ZipFile zipFile) {
    Enumeration<? extends ZipEntry> e = zipFile.entries();
    while (e.hasMoreElements()) {
      String name = ((ZipEntry)e.nextElement()).getName();
      if (this.filenameValidator.getFileNameValidator().filenameHasExecutableType(name))
        return true; 
    } 
    return false;
  }
  
  private boolean zipExceedSizeLimit(ZipFile zipFile) {
    long totalSize = 0L;
    int zipEentryCount = 0;
    long MAX_ZIP_SIZE = this.magicInfoProperties.getMaxSizeOfZip();
    Enumeration<? extends ZipEntry> e = zipFile.entries();
    int MAX_ZIP_FILES_QUANTITIY = this.magicInfoProperties.getMaxQuantitiyOfZipFiles();
    while (e.hasMoreElements()) {
      zipEentryCount++;
      totalSize += ((ZipEntry)e.nextElement()).getSize();
      if (totalSize > MAX_ZIP_SIZE || zipEentryCount > MAX_ZIP_FILES_QUANTITIY)
        return true; 
    } 
    return false;
  }
}
