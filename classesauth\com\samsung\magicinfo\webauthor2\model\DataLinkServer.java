package classes.com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.samsung.magicinfo.webauthor2.exception.service.CannotFindDataLinkTableException;
import com.samsung.magicinfo.webauthor2.model.DataLinkTable;
import com.samsung.magicinfo.webauthor2.repository.model.DatalinkServerEntityData;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class DataLinkServer {
  private final String serverName;
  
  private Boolean hasDataLinkTable = Boolean.valueOf(true);
  
  private Boolean hasDataViewTable = Boolean.valueOf(false);
  
  @JsonIgnore
  private final String ipAddress;
  
  @JsonIgnore
  private final Integer port;
  
  @JsonIgnore
  private final Integer period;
  
  @JsonIgnore
  private final Boolean useSsl;
  
  @JsonIgnore
  private final Integer ftpPort;
  
  @JsonIgnore
  private final Boolean bypass;
  
  @JsonIgnore
  private final String privateIpAddress;
  
  @JsonIgnore
  private final Integer privateWebPort;
  
  @JsonIgnore
  private final Boolean privateMode;
  
  @JsonIgnore
  private Map<String, DataLinkTable> tablesMap;
  
  @JsonIgnore
  private List<String> serviceListDatalink;
  
  @JsonIgnore
  private List<String> serviceListDataView;
  
  @JsonIgnore
  private Map<String, List<DataLinkTable>> servicesMap;
  
  public DataLinkServer(String serverName, String ipAddress, Integer port, Integer period, Boolean useSsl, Integer ftpPort, Boolean bypass, String privateIpAddress, Integer privateWebPort, Boolean privateMode) {
    this.serverName = serverName;
    this.ipAddress = ipAddress;
    this.port = port;
    this.period = period;
    this.useSsl = useSsl;
    this.ftpPort = ftpPort;
    this.bypass = bypass;
    this.privateIpAddress = privateIpAddress;
    this.privateWebPort = privateWebPort;
    this.privateMode = privateMode;
    this.tablesMap = new HashMap<>();
    this.serviceListDatalink = new ArrayList<>();
    this.serviceListDataView = new ArrayList<>();
    this.servicesMap = new HashMap<>();
  }
  
  @JsonIgnore
  public List<DataLinkTable> getTables(String dataType) {
    List<DataLinkTable> tablesAll = new ArrayList<>(this.tablesMap.values());
    List<DataLinkTable> tables = new ArrayList<>();
    if (dataType.equalsIgnoreCase("dataView")) {
      for (DataLinkTable table : tablesAll) {
        if (table.getIsDataView().booleanValue())
          tables.add(table); 
      } 
    } else {
      for (DataLinkTable table : tablesAll) {
        if (!table.getIsDataView().booleanValue())
          tables.add(table); 
      } 
    } 
    return tables;
  }
  
  @JsonIgnore
  public List<DataLinkTable> getTablesForService(String name, String dataType) {
    List<DataLinkTable> tablesAll = this.servicesMap.get(name);
    List<DataLinkTable> tables = new ArrayList<>();
    if (dataType.equalsIgnoreCase("dataView")) {
      for (DataLinkTable table : tablesAll) {
        if (table.getIsDataView().booleanValue())
          tables.add(table); 
      } 
    } else {
      for (DataLinkTable table : tablesAll) {
        if (!table.getIsDataView().booleanValue())
          tables.add(table); 
      } 
    } 
    return tables;
  }
  
  @JsonIgnore
  public DataLinkTable getTable(String dynaName) {
    DataLinkTable table = this.tablesMap.get(dynaName);
    if (table == null)
      throw new CannotFindDataLinkTableException(dynaName); 
    return table;
  }
  
  public String getServerName() {
    return this.serverName;
  }
  
  public Boolean getHasDataLinkTable() {
    return this.hasDataLinkTable;
  }
  
  public Boolean getHasDataViewTable() {
    return this.hasDataViewTable;
  }
  
  public String getIpAddress() {
    return this.ipAddress;
  }
  
  public Integer getPort() {
    return this.port;
  }
  
  public Integer getPeriod() {
    return this.period;
  }
  
  public Boolean getUseSsl() {
    return this.useSsl;
  }
  
  public Integer getFtpPort() {
    return this.ftpPort;
  }
  
  public Boolean getBypass() {
    return this.bypass;
  }
  
  public String getPrivateIpAddress() {
    return this.privateIpAddress;
  }
  
  public Integer getPrivateWebPort() {
    return this.privateWebPort;
  }
  
  public Boolean getPrivateMode() {
    return this.privateMode;
  }
  
  public void setTablesMap(Map<String, DataLinkTable> tablesMap) {
    this.tablesMap = tablesMap;
    updateDataTypes();
  }
  
  private void updateDataTypes() {
    this.hasDataLinkTable = Boolean.valueOf(false);
    this.hasDataViewTable = Boolean.valueOf(false);
    List<DataLinkTable> tablesAll = new ArrayList<>(this.tablesMap.values());
    for (DataLinkTable table : tablesAll) {
      if (this.hasDataLinkTable.booleanValue() && this.hasDataViewTable.booleanValue())
        break; 
      if (table.getIsDataView().booleanValue()) {
        this.hasDataViewTable = Boolean.valueOf(true);
        continue;
      } 
      this.hasDataLinkTable = Boolean.valueOf(true);
    } 
  }
  
  public List<String> getServiceList(String dataType) {
    if (dataType.equalsIgnoreCase("dataView"))
      return this.serviceListDataView; 
    return this.serviceListDatalink;
  }
  
  public void setServiceList(List<String> serviceList) {
    this.serviceListDatalink.clear();
    this.serviceListDataView.clear();
    for (String name : serviceList) {
      List<DataLinkTable> tables = this.servicesMap.get(name);
      boolean hasDatalink = false;
      boolean hasDataView = false;
      for (DataLinkTable table : tables) {
        if (hasDataView && hasDatalink)
          break; 
        if (table.getIsDataView().booleanValue()) {
          hasDataView = true;
          continue;
        } 
        hasDatalink = true;
      } 
      if (hasDatalink)
        this.serviceListDatalink.add(name); 
      if (hasDataView)
        this.serviceListDataView.add(name); 
    } 
  }
  
  public Map<String, List<DataLinkTable>> getServicesMap() {
    return this.servicesMap;
  }
  
  public void setServicesMap(Map<String, List<DataLinkTable>> servicesMap) {
    this.servicesMap = servicesMap;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.DataLinkServer fromData(DatalinkServerEntityData data) {
    return new com.samsung.magicinfo.webauthor2.model.DataLinkServer(data.getServerName(), data.getIpAddress(), data.getPort(), data.getPeriod(), data
        .getUseSsl(), data.getFtpPort(), data.getBypass(), data.getPrivateIpAddress(), data
        .getPrivateWebPort(), data.getPrivateMode());
  }
  
  public static List<com.samsung.magicinfo.webauthor2.model.DataLinkServer> fromData(List<DatalinkServerEntityData> datas) {
    List<com.samsung.magicinfo.webauthor2.model.DataLinkServer> dataLinkServers = new ArrayList<>();
    for (DatalinkServerEntityData data : datas)
      dataLinkServers.add(fromData(data)); 
    return dataLinkServers;
  }
}
