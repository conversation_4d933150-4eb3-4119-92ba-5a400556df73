package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.exception.repository.IllegalOpenApiMethodType;
import com.samsung.magicinfo.webauthor2.repository.OpenApiMethodType;
import java.net.URI;
import java.util.Collections;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

public abstract class OpenApiMethod<T, R> {
  private static final char DOT_CHAR = '.';
  
  private OpenApiMethodType openApiMethodType;
  
  private final RestTemplate restTemplate;
  
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.repository.OpenApiMethod.class);
  
  protected OpenApiMethod(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
    this.openApiMethodType = OpenApiMethodType.GET;
  }
  
  protected OpenApiMethod(RestTemplate restTemplate, OpenApiMethodType openApiMethodType) {
    this.restTemplate = restTemplate;
    this.openApiMethodType = openApiMethodType;
  }
  
  protected abstract String getOpenApiClassName();
  
  protected abstract String getOpenApiMethodName();
  
  protected abstract Map<String, String> getRequestParams();
  
  private URI getRestPath() {
    UriComponentsBuilder uriComponentsBuilder = UriComponentsBuilder.newInstance().path("/openapi/open").queryParam("service", new Object[] { getOpenApiClassName() + '.' + getOpenApiMethodName() });
    Map<String, String> parameterMap = getRequestParams();
    MultiValueMap<String, String> parameterMultiValueMap = convertMapToMultiValueMap(parameterMap);
    uriComponentsBuilder.queryParams(parameterMultiValueMap);
    return uriComponentsBuilder.build().encode().toUri();
  }
  
  private MultiValueMap<String, String> convertMapToMultiValueMap(Map<String, String> requestParams) {
    LinkedMultiValueMap linkedMultiValueMap = new LinkedMultiValueMap();
    for (Map.Entry<String, String> entry : requestParams.entrySet())
      linkedMultiValueMap.add(entry.getKey(), entry.getValue()); 
    return (MultiValueMap<String, String>)linkedMultiValueMap;
  }
  
  abstract Class<R> getResponseClass();
  
  abstract T convertResponseData(R paramR);
  
  public T callMethod() {
    R responseData = callRestMethod();
    return convertResponseData(responseData);
  }
  
  private R callRestMethod() {
    URI uri;
    HttpHeaders headers;
    HttpEntity<String> request;
    switch (null.$SwitchMap$com$samsung$magicinfo$webauthor2$repository$OpenApiMethodType[this.openApiMethodType.ordinal()]) {
      case 1:
        uri = getRestPath();
        return (R)this.restTemplate.getForObject(uri, getResponseClass());
      case 2:
        headers = new HttpHeaders();
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_XML));
        request = new HttpEntity((MultiValueMap)headers);
        uri = getRestPath();
        logger.debug("REQUEST: " + request.toString());
        return (R)this.restTemplate.postForObject(uri, request, getResponseClass());
    } 
    throw new IllegalOpenApiMethodType("Unallowed OpenApi method type");
  }
}
