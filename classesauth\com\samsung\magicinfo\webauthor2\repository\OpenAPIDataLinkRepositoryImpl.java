package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.AddDlkInfoOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetDataLinkServerListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIDataLinkRepository;
import com.samsung.magicinfo.webauthor2.repository.model.DatalinkServerEntityData;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.List;
import javax.inject.Inject;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPIDataLinkRepositoryImpl implements OpenAPIDataLinkRepository {
  private final RestTemplate restTemplate;
  
  private final UserData userData;
  
  @Inject
  public OpenAPIDataLinkRepositoryImpl(RestTemplate restTemplate, UserData userData) {
    this.restTemplate = restTemplate;
    this.userData = userData;
  }
  
  public List<DatalinkServerEntityData> getDataLinkServerEntities() {
    GetDataLinkServerListOpenApiMethod openApiMethod = new GetDataLinkServerListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken());
    return (List<DatalinkServerEntityData>)openApiMethod.callMethod();
  }
  
  public void addDlkInfo(String dlkContentId, String dlkVersionId, String lftContentId, List<String> contentsList, String convertTableXml) {
    AddDlkInfoOpenApiMethod openApiMethod = new AddDlkInfoOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), dlkContentId, dlkVersionId, lftContentId, contentsList, convertTableXml);
    openApiMethod.callMethod();
  }
}
