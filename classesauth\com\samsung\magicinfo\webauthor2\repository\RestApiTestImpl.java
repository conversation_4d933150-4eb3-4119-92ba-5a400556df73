package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.endpoint.RestApi;
import com.samsung.magicinfo.webauthor2.repository.RestApiTest;
import com.samsung.magicinfo.webauthor2.repository.model.RestApiAuthResponse;
import com.samsung.magicinfo.webauthor2.repository.model.RestApiHardwareUniqueKeyResponse;
import java.net.URI;
import java.util.Collections;
import javax.inject.Inject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Repository;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.client.UnknownHttpStatusCodeException;
import org.springframework.web.util.UriComponentsBuilder;

@Repository
public class RestApiTestImpl implements RestApiTest {
  private final RestTemplate restTemplate;
  
  private static final Logger LOGGER = LoggerFactory.getLogger(RestApi.class);
  
  @Inject
  public RestApiTestImpl(RestTemplate restTemplate) {
    this.restTemplate = restTemplate;
  }
  
  public String getLoginToken(String username, String password) {
    URI uri = UriComponentsBuilder.newInstance().path("/auth").build().encode().toUri();
    LOGGER.info("uri: " + uri.toString());
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(Collections.singletonList(MediaType.ALL));
    StringBuilder sb = new StringBuilder("{");
    sb.append("\"username\"").append(":").append("\"").append(username).append("\"");
    sb.append(",");
    sb.append("\"password\"").append(":").append("\"").append(password).append("\"");
    sb.append("}");
    HttpEntity<String> entity = new HttpEntity(sb.toString(), (MultiValueMap)headers);
    LOGGER.info("REQUEST: " + entity.toString());
    String token = "";
    try {
      RestApiAuthResponse response = (RestApiAuthResponse)this.restTemplate.postForObject(uri, entity, RestApiAuthResponse.class);
      token = response.getToken();
    } catch (UnknownHttpStatusCodeException ex) {
      LOGGER.info("UnknownHttpStatusCodeException: " + ex.getMessage());
    } catch (RestClientException ex) {
      LOGGER.info("RestClientException: " + ex.getMessage());
    } catch (Exception ex) {
      LOGGER.info("Exception: " + ex.getMessage());
    } 
    return token;
  }
  
  public String getHardwareUniqueKey(String token) {
    URI uri = UriComponentsBuilder.newInstance().path("/restapi/v2.0/ems/settings/licenses/HWUniqueKey").build().encode().toUri();
    LOGGER.info("uri: " + uri.toString());
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    headers.setAccept(Collections.singletonList(MediaType.ALL));
    headers.set("api_key", token);
    HttpEntity<String> entity = new HttpEntity((MultiValueMap)headers);
    LOGGER.info("REQUEST: " + entity.toString());
    String hardwareUniqueKey = "unknown.hw.key";
    try {
      ResponseEntity<RestApiHardwareUniqueKeyResponse> response = this.restTemplate.exchange(uri, HttpMethod.GET, entity, RestApiHardwareUniqueKeyResponse.class);
      RestApiHardwareUniqueKeyResponse responseBody = (RestApiHardwareUniqueKeyResponse)response.getBody();
      hardwareUniqueKey = responseBody.getItems();
    } catch (UnknownHttpStatusCodeException ex) {
      LOGGER.info("UnknownHttpStatusCodeException: " + ex.getMessage());
    } catch (RestClientException ex) {
      LOGGER.info("RestClientException: " + ex.getMessage());
    } catch (Exception ex) {
      LOGGER.info("Exception: " + ex.getMessage());
    } 
    return hardwareUniqueKey;
  }
}
