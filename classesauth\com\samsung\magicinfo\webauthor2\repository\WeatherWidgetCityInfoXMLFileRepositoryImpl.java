package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.weather.CityData;
import com.samsung.magicinfo.webauthor2.model.weather.CityList;
import com.samsung.magicinfo.webauthor2.model.weather.Country;
import com.samsung.magicinfo.webauthor2.model.weather.CountryData;
import com.samsung.magicinfo.webauthor2.repository.WeatherWidgetCityInfoXMLFileRepository;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Unmarshaller;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

@Service
public class WeatherWidgetCityInfoXMLFileRepositoryImpl implements WeatherWidgetCityInfoXMLFileRepository {
  private static final String CITY_DEFINITIONS_FILE = "accuweather/AccuCityInfo_";
  
  private static final String CITY_DEFINITIONS_FILE_EXT = ".xml";
  
  private String languageCode = "default";
  
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.repository.WeatherWidgetCityInfoXMLFileRepositoryImpl.class);
  
  private List<CountryData> countryDataList = null;
  
  private List<Country> countries = null;
  
  public List<CountryData> getCountryDataList() {
    if (this.countryDataList == null || this.countryDataList.isEmpty())
      initCountryList(); 
    return this.countryDataList;
  }
  
  public List<Country> getCountryList() {
    if (this.countries == null || this.countries.isEmpty())
      initCountryList(); 
    return this.countries;
  }
  
  public List<CityData> getCitiesForCountryIndex(int index) {
    if (this.countries == null || this.countries.isEmpty())
      initCountryList(); 
    return ((CountryData)this.countryDataList.get(index)).getCities();
  }
  
  private void initCountryList() {
    try {
      logger.debug("WeatherWidget file repository called for country list initialization, language: " + this.languageCode + ".");
      this.countryDataList = null;
      this.countries = new ArrayList<>();
      JAXBContext jaxbContext = JAXBContext.newInstance(new Class[] { CityList.class });
      Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
      File xmlFile = (new ClassPathResource("accuweather/AccuCityInfo_" + this.languageCode + ".xml")).getFile();
      CityList cities = (CityList)jaxbUnmarshaller.unmarshal(xmlFile);
      if (!cities.getCountries().isEmpty()) {
        this.countryDataList = cities.getCountries();
        for (int i = 0; i < this.countryDataList.size(); i++) {
          this.countries.add(new Country(i, ((CountryData)this.countryDataList.get(i)).getName()));
          for (int j = 0; j < ((CountryData)this.countryDataList.get(i)).getCities().size(); j++)
            ((CityData)((CountryData)this.countryDataList.get(i)).getCities().get(j)).setIndex(j); 
        } 
      } 
    } catch (JAXBException|java.io.IOException ex) {
      logger.error("Couldn't parse City List data: " + ex.getMessage());
    } 
  }
}
