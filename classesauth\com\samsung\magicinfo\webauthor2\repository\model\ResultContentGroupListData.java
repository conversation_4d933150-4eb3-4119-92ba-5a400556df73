package classes.com.samsung.magicinfo.webauthor2.repository.model;

import com.samsung.magicinfo.webauthor2.repository.model.ContentGroupData;
import java.io.Serializable;
import java.util.List;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlElementWrapper;

public class ResultContentGroupListData implements Serializable {
  @XmlElement
  private Long totalCount;
  
  @XmlElementWrapper(name = "resultList")
  @XmlElement(name = "Group")
  private List<ContentGroupData> resultList;
  
  public Long getTotalCount() {
    return this.totalCount;
  }
  
  public List<ContentGroupData> getResultList() {
    return this.resultList;
  }
}
