package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.model.Content;
import com.samsung.magicinfo.webauthor2.model.ContentGroup;
import com.samsung.magicinfo.webauthor2.model.ContentThumbnailBasic;
import com.samsung.magicinfo.webauthor2.model.DeviceType;
import com.samsung.magicinfo.webauthor2.model.factory.ContentFactory;
import com.samsung.magicinfo.webauthor2.repository.DeleteContentOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetContentGroupListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetContentInfoOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetContentListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetContentThumbnailByFileIdOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetContentThumbnailsOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetDLKMappingListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.GetMediaTypeListOpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.OpenAPIContentRepository2;
import com.samsung.magicinfo.webauthor2.repository.model.ContentData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultContentGroupListData;
import com.samsung.magicinfo.webauthor2.repository.model.ResultListData;
import com.samsung.magicinfo.webauthor2.repository.model.criteria.ContentSearchCriteria;
import com.samsung.magicinfo.webauthor2.util.JaxbUtil;
import com.samsung.magicinfo.webauthor2.util.UserData;
import java.util.List;
import javax.inject.Inject;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;
import org.springframework.web.client.RestTemplate;

@Repository
public class OpenAPIContentRepository2Impl implements OpenAPIContentRepository2 {
  private RestTemplate restTemplate;
  
  private UserData userData;
  
  private JaxbUtil jaxbUtil;
  
  private ContentFactory contentFactory;
  
  @Inject
  public OpenAPIContentRepository2Impl(RestTemplate restTemplate, UserData userData, JaxbUtil jaxbUtil, ContentFactory contentFactory) {
    this.restTemplate = restTemplate;
    this.userData = userData;
    this.jaxbUtil = jaxbUtil;
    this.contentFactory = contentFactory;
  }
  
  public ContentData getContent(String contentId) {
    GetContentInfoOpenApiMethod openApiMethod = new GetContentInfoOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), contentId);
    return (ContentData)openApiMethod.callMethod();
  }
  
  public Page<Content> getContentList(Pageable pageable, DeviceType deviceType, ContentSearchCriteria searchCriteria) {
    GetContentListOpenApiMethod openApiMethod = new GetContentListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), deviceType, searchCriteria, this.jaxbUtil);
    ResultListData contentData = (ResultListData)openApiMethod.callMethod();
    List<Content> contentList = this.contentFactory.fromData(contentData.getResultList());
    return (Page<Content>)new PageImpl(contentList, pageable, contentData.getTotalCount().longValue());
  }
  
  public List<ContentGroup> getContentGroupList() {
    GetContentGroupListOpenApiMethod openApiMethod = new GetContentGroupListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken());
    ResultContentGroupListData contentGroupData = (ResultContentGroupListData)openApiMethod.callMethod();
    return this.contentFactory.fromGroupData(contentGroupData.getResultList());
  }
  
  public List<ContentData> getRelatedDLKContent(String lftContentId) {
    GetDLKMappingListOpenApiMethod openApiMethod = new GetDLKMappingListOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), lftContentId);
    return (List<ContentData>)openApiMethod.callMethod();
  }
  
  public List<ContentThumbnailBasic> getContentThumbnails(String contentId, String resolution) {
    GetContentThumbnailsOpenApiMethod openApiMethod = new GetContentThumbnailsOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), contentId, resolution);
    return (List<ContentThumbnailBasic>)openApiMethod.callMethod();
  }
  
  public ContentThumbnailBasic getContentThumbnail(String fileId, String size) {
    GetContentThumbnailByFileIdOpenApiMethod openApiMethod = new GetContentThumbnailByFileIdOpenApiMethod(this.restTemplate, this.userData.getToken(), fileId, size);
    return (ContentThumbnailBasic)openApiMethod.callMethod();
  }
  
  public List<String> getMediaTypeList(DeviceType deviceType) {
    GetMediaTypeListOpenApiMethod openApiMethod = new GetMediaTypeListOpenApiMethod(this.restTemplate, this.userData.getToken(), deviceType);
    return (List<String>)openApiMethod.callMethod();
  }
  
  public String deleteContent(String contentId) {
    DeleteContentOpenApiMethod openApiMethod = new DeleteContentOpenApiMethod(this.restTemplate, this.userData.getUserId(), this.userData.getToken(), contentId);
    return (String)openApiMethod.callMethod();
  }
}
