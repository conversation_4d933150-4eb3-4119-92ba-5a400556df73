package classes.com.samsung.magicinfo.webauthor2.model;

import com.samsung.magicinfo.webauthor2.model.DataLinkTableColumn;
import com.samsung.magicinfo.webauthor2.repository.model.dlk.DLKTableRowData;
import java.util.ArrayList;
import java.util.List;

public class DataLinkTableRow {
  private final List<DataLinkTableColumn> columns;
  
  public DataLinkTableRow(List<DataLinkTableColumn> columns) {
    this.columns = columns;
  }
  
  public List<DataLinkTableColumn> getColumns() {
    return this.columns;
  }
  
  public static com.samsung.magicinfo.webauthor2.model.DataLinkTableRow fromData(DLKTableRowData data) {
    return new com.samsung.magicinfo.webauthor2.model.DataLinkTableRow(DataLinkTableColumn.fromData(data.getColumns()));
  }
  
  public static List<com.samsung.magicinfo.webauthor2.model.DataLinkTableRow> fromData(List<DLKTableRowData> dataList) {
    List<com.samsung.magicinfo.webauthor2.model.DataLinkTableRow> rows = new ArrayList<>();
    for (DLKTableRowData data : dataList)
      rows.add(fromData(data)); 
    return rows;
  }
}
