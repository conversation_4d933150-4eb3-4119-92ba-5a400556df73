package classes.com.samsung.magicinfo.webauthor2.model.datalink;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.datalink.Value;
import com.samsung.magicinfo.webauthor2.model.datalink.ValueType;
import org.apache.commons.io.FilenameUtils;

public class FolderValue extends Value {
  public static final String DESCRIPTOR_FILE_EXTENSION = "rlf";
  
  private final String rlfFileName;
  
  private final String rlfFileId;
  
  private final long fileSize;
  
  @JsonCreator
  public FolderValue(@JsonProperty("rlfFileName") String rlfFileName, @JsonProperty("rlfFileId") String rlfFileId, @JsonProperty("fileSize") long fileSize) {
    super(ValueType.FOLDER);
    if (!FilenameUtils.getExtension(rlfFileName).equals("rlf"))
      throw new IllegalArgumentException("File name must have rlf extention for folder value!"); 
    this.rlfFileName = rlfFileName;
    this.rlfFileId = rlfFileId;
    this.fileSize = fileSize;
  }
  
  public String getRlfFileName() {
    return this.rlfFileName;
  }
  
  public String getRlfFileId() {
    return this.rlfFileId;
  }
  
  public long getFileSize() {
    return this.fileSize;
  }
}
