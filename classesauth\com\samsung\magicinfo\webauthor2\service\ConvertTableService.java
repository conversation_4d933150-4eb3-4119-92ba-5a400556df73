package classes.com.samsung.magicinfo.webauthor2.service;

import com.samsung.magicinfo.webauthor2.model.ConvertTable;
import java.util.List;

public interface ConvertTableService {
  List<ConvertTable> getConvertTableList();
  
  String addConvertTable(ConvertTable paramConvertTable);
  
  String delete(String paramString);
  
  String modify(ConvertTable paramConvertTable1, ConvertTable paramConvertTable2);
}
