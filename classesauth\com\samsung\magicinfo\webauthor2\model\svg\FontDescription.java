package classes.com.samsung.magicinfo.webauthor2.model.svg;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import java.io.Serializable;

public class FontDescription implements Serializable {
  private static final long serialVersionUID = 8312254292950891514L;
  
  private String fontFileName;
  
  private String fontFace;
  
  private String fontFamily;
  
  private String fontLogicalName;
  
  private String fontWeight;
  
  private String fontStyle;
  
  public FontDescription() {}
  
  @JsonCreator
  public FontDescription(@JsonProperty(value = "fontFileName", required = false) String fontFileName, @JsonProperty(value = "fontFace", required = false) String fontFace, @JsonProperty(value = "fontFamily", required = false) String fontFamily, @JsonProperty(value = "fontLogicalName", required = false) String fontLogicalName, @JsonProperty(value = "fontWeight", required = true) String fontWeight, @JsonProperty(value = "fontStyle", required = true) String fontStyle) {
    this.fontFileName = fontFileName;
    this.fontFace = fontFace;
    this.fontFamily = fontFamily;
    this.fontLogicalName = fontLogicalName;
    this.fontWeight = fontWeight;
    this.fontStyle = fontStyle;
  }
  
  public String getFontFileName() {
    return this.fontFileName;
  }
  
  public void setFontFileName(String fontFileName) {
    this.fontFileName = fontFileName;
  }
  
  public String getFontFace() {
    return this.fontFace;
  }
  
  public void setFontFace(String fontFace) {
    this.fontFace = fontFace;
  }
  
  public String getFontFamily() {
    return this.fontFamily;
  }
  
  public void setFontFamily(String fontFamily) {
    this.fontFamily = fontFamily;
  }
  
  public String getFontLogicalName() {
    return this.fontLogicalName;
  }
  
  public void setFontLogicalName(String fontLogicalName) {
    this.fontLogicalName = fontLogicalName;
  }
  
  public String getFontWeight() {
    return this.fontWeight;
  }
  
  public void setFontWeight(String fontWeight) {
    this.fontWeight = fontWeight;
  }
  
  public String getFontStyle() {
    return this.fontStyle;
  }
  
  public void setFontStyle(String fontStyle) {
    this.fontStyle = fontStyle;
  }
  
  public String toString() {
    return "FontDescriptor [fontFileName=" + this.fontFileName + ", fontFace=" + this.fontFace + ", fontFamily=" + this.fontFamily + ", fontLogicalName=" + this.fontLogicalName + ", fontWeight=" + this.fontWeight + ", fontStyle=" + this.fontStyle + "]";
  }
}
