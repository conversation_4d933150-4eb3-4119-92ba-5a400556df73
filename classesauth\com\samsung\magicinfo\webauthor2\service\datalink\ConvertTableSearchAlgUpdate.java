package classes.com.samsung.magicinfo.webauthor2.service.datalink;

import com.samsung.magicinfo.webauthor2.model.datalink.ConvertTable;
import com.samsung.magicinfo.webauthor2.service.datalink.ConvertTableSearchAlg;
import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableListType;
import com.samsung.magicinfo.webauthor2.xml.dlkinfo.ConvertTableMapType;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

public class ConvertTableSearchAlgUpdate extends ConvertTableSearchAlg<Void> {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.datalink.ConvertTableSearchAlgUpdate.class);
  
  private List<ConvertTableMapType> convertTableMapTypeList;
  
  public ConvertTableSearchAlgUpdate(ConvertTableListType convertTableList) {
    Assert.notNull(convertTableList, "convertTableList cannot be null");
    Assert.notNull(convertTableList.getConvertTableMap(), "ConvertTableMap cannot be null");
    this.convertTableMapTypeList = convertTableList.getConvertTableMap();
  }
  
  protected void actionOnConvertTable(ConvertTable convertTable, int pageNumb, String elementName, int splitGroupId, String splitGroupName, int dataIndex, String convertTableName) {
    boolean found = false;
    for (ConvertTableMapType convertTableMap : this.convertTableMapTypeList) {
      if (convertTableMap.getPageNumb() == pageNumb && 
        convertTableMap.getElementName() != null && convertTableMap.getElementName().equals(elementName) && 
        convertTableMap.getSplitGroupId() == splitGroupId && 
        convertTableMap.getSplitGroupName() != null && convertTableMap.getSplitGroupName().equals(splitGroupName) && 
        convertTableMap.getDataIndex() == dataIndex) {
        convertTable.setName(convertTableName);
        found = true;
        break;
      } 
    } 
    if (!found) {
      logger.error("Cannot find proper convert table data");
      logger.error("pageNumb=" + pageNumb + " elementName=" + elementName + " splitGroupId=" + splitGroupId + " splitGroupName=" + splitGroupName + " dataIndex=" + dataIndex + " convertTableName=" + convertTableName);
    } 
  }
  
  public Void getResult() {
    return null;
  }
}
