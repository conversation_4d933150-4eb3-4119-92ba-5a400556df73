package classes.com.samsung.magicinfo.webauthor2.model;

import com.google.common.base.Strings;
import com.google.common.collect.ImmutableList;
import java.util.ArrayList;
import java.util.List;

public enum MediaType {
  IMAGE, MOVIE, OFFICE, FLASH, SOUND, PDF, LFD, LFT, DLK, VWL, FTP, CIFS, STRM, TLFD, HTML, URL, FONT, PLUGIN_EFFECT;
  
  public static List<com.samsung.magicinfo.webauthor2.model.MediaType> BASIC_MEDIA_TYPE_LIST;
  
  static {
    BASIC_MEDIA_TYPE_LIST = (List<com.samsung.magicinfo.webauthor2.model.MediaType>)ImmutableList.of(IMAGE, MOVIE, OFFICE, FLASH, SOUND, PDF, FTP, CIFS, HTML, URL, FONT);
  }
  
  public static List<com.samsung.magicinfo.webauthor2.model.MediaType> fromStringList(List<String> mediaTypes) {
    if (mediaTypes == null || mediaTypes.isEmpty())
      return BASIC_MEDIA_TYPE_LIST; 
    List<com.samsung.magicinfo.webauthor2.model.MediaType> list = new ArrayList<>();
    for (String s : mediaTypes) {
      if (Strings.isNullOrEmpty(s))
        continue; 
      list.add(valueOf(s));
    } 
    if (list.isEmpty())
      return BASIC_MEDIA_TYPE_LIST; 
    return list;
  }
}
