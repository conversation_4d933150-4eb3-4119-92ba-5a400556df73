package classes.com.samsung.magicinfo.webauthor2.repository;

import com.samsung.magicinfo.webauthor2.repository.OpenApiMethod;
import com.samsung.magicinfo.webauthor2.repository.model.UserInfo;
import com.samsung.magicinfo.webauthor2.repository.model.UserInfoResponseData;
import java.util.HashMap;
import java.util.Map;
import org.springframework.web.client.RestTemplate;

public class GetUserInfoOpenApiMethod extends OpenApiMethod<UserInfo, UserInfoResponseData> {
  private String token;
  
  private String userId;
  
  public GetUserInfoOpenApiMethod(RestTemplate restTemplate, String token, String userId) {
    super(restTemplate);
    this.token = token;
    this.userId = userId;
  }
  
  protected String getOpenApiClassName() {
    return "CommonUserService";
  }
  
  protected String getOpenApiMethodName() {
    return "getUserInfo";
  }
  
  protected Map<String, String> getRequestParams() {
    Map<String, String> vars = new HashMap<>();
    vars.put("token", this.token);
    vars.put("userId", this.userId);
    return vars;
  }
  
  Class<UserInfoResponseData> getResponseClass() {
    return UserInfoResponseData.class;
  }
  
  UserInfo convertResponseData(UserInfoResponseData responseData) {
    return responseData.getUser();
  }
}
