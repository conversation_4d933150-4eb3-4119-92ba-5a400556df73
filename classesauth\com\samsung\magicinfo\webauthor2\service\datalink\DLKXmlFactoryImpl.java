package classes.com.samsung.magicinfo.webauthor2.service.datalink;

import com.samsung.magicinfo.webauthor2.model.datalink.DataLinkDescriptor;
import com.samsung.magicinfo.webauthor2.service.datalink.DLKXmlFactory;
import com.samsung.magicinfo.webauthor2.service.datalink.DLKXmlObjMapper;
import com.samsung.magicinfo.webauthor2.service.datalink.ExtendedCharacterEscapeHandler;
import com.samsung.magicinfo.webauthor2.xml.datalink.DataLinkContentMetaType;
import java.io.Reader;
import java.io.StringReader;
import javax.inject.Inject;
import javax.xml.bind.JAXBContext;
import javax.xml.bind.JAXBException;
import javax.xml.bind.Marshaller;
import javax.xml.transform.Result;
import javax.xml.transform.stream.StreamSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.oxm.jaxb.Jaxb2Marshaller;
import org.springframework.stereotype.Service;
import org.springframework.xml.transform.StringResult;

@Service
public class DLKXmlFactoryImpl implements DLKXmlFactory {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.service.datalink.DLKXmlFactoryImpl.class);
  
  private static String XML_CUSTOM_FRAGMENT = "<?xml version=\"1.0\" encoding=\"utf-8\" standalone=\"no\"?>\n";
  
  private final DLKXmlObjMapper dlkXmlObjMapper;
  
  private final Jaxb2Marshaller jaxb2MarshallerForDLK;
  
  @Inject
  public DLKXmlFactoryImpl(DLKXmlObjMapper dlkXmlObjMapper, Jaxb2Marshaller jaxb2MarshallerForDLK) {
    this.dlkXmlObjMapper = dlkXmlObjMapper;
    this.jaxb2MarshallerForDLK = jaxb2MarshallerForDLK;
  }
  
  public String marshalSpring(DataLinkDescriptor dataLinkDescriptor) {
    DataLinkContentMetaType xmlObj = this.dlkXmlObjMapper.toXmlObject(dataLinkDescriptor);
    StringResult result = new StringResult();
    this.jaxb2MarshallerForDLK.marshal(xmlObj, (Result)result);
    StringBuilder xmlStringBuilder = new StringBuilder(XML_CUSTOM_FRAGMENT);
    xmlStringBuilder.append(result);
    return xmlStringBuilder.toString();
  }
  
  public DataLinkDescriptor unmarshal(String xml) {
    Reader stringReader = new StringReader(xml);
    DataLinkContentMetaType xmlObj = null;
    xmlObj = (DataLinkContentMetaType)this.jaxb2MarshallerForDLK.unmarshal(new StreamSource(stringReader));
    return this.dlkXmlObjMapper.fromXmlObject(xmlObj);
  }
  
  public String marshal(DataLinkDescriptor dataLinkDescriptor) {
    DataLinkContentMetaType xmlObj = this.dlkXmlObjMapper.toXmlObject(dataLinkDescriptor);
    StringResult result = new StringResult();
    try {
      JAXBContext jc = JAXBContext.newInstance(new Class[] { DataLinkContentMetaType.class });
      Marshaller marshaller = jc.createMarshaller();
      marshaller.setProperty("jaxb.formatted.output", Boolean.valueOf(true));
      marshaller.setProperty("jaxb.fragment", Boolean.valueOf(true));
      marshaller.setProperty("jaxb.encoding", "UTF-8");
      marshaller.setProperty("com.sun.xml.bind.marshaller.CharacterEscapeHandler", new ExtendedCharacterEscapeHandler());
      marshaller.marshal(xmlObj, (Result)result);
    } catch (JAXBException ex) {
      logger.error(ex.getMessage());
    } 
    StringBuilder xmlStringBuilder = new StringBuilder(XML_CUSTOM_FRAGMENT);
    xmlStringBuilder.append(result.toString());
    return xmlStringBuilder.toString();
  }
}
