package classes.com.samsung.magicinfo.webauthor2.webapi.controller;

import com.samsung.magicinfo.webauthor2.exception.service.UploaderException;
import com.samsung.magicinfo.webauthor2.model.ContentSaveElements;
import com.samsung.magicinfo.webauthor2.model.UploadResponse;
import com.samsung.magicinfo.webauthor2.service.UploadFileNameValidationService;
import com.samsung.magicinfo.webauthor2.service.thumbnail.ThumbnailService;
import java.nio.file.Path;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

@Controller
public class ThumbnailController {
  private static final Logger logger = LoggerFactory.getLogger(com.samsung.magicinfo.webauthor2.webapi.controller.ThumbnailController.class);
  
  private final UploadFileNameValidationService uploadFileNameValidationService;
  
  private ThumbnailService thumbnailService;
  
  private ContentSaveElements contentSaveElements;
  
  @Autowired
  public ThumbnailController(ThumbnailService thumbnailService, ContentSaveElements contentSaveElements, UploadFileNameValidationService uploadFileNameValidationService) {
    this.thumbnailService = thumbnailService;
    this.contentSaveElements = contentSaveElements;
    this.uploadFileNameValidationService = uploadFileNameValidationService;
  }
  
  @PostMapping({"/thumbnailBasic"})
  public HttpEntity<UploadResponse> projectThumbnail(@RequestParam("byteCode") String byteCode, @RequestParam("fileName") String projectName, @RequestParam(value = "isTemplate", required = false) Boolean isTemplate, @RequestParam(value = "isVwl", required = false) Boolean isVwl) throws UploaderException {
    String projectThumbnailPathToString;
    if (false == this.uploadFileNameValidationService.validateFileNameNotToMoveIntoUpperFolder(projectName).booleanValue()) {
      UploadResponse uploadResponse = new UploadResponse(400, "Invalid thumbnail name");
      return (HttpEntity<UploadResponse>)ResponseEntity.status(HttpStatus.BAD_REQUEST).body(uploadResponse);
    } 
    Path projectThumbnailPath = this.thumbnailService.createProjectThumbnail(byteCode, projectName, isTemplate, isVwl);
    if (projectThumbnailPath == null) {
      projectThumbnailPathToString = "";
    } else {
      projectThumbnailPathToString = projectThumbnailPath.toString();
    } 
    this.contentSaveElements.setPathTothumbnail(projectThumbnailPathToString);
    return (HttpEntity<UploadResponse>)ResponseEntity.ok().body(new UploadResponse(200, "thumbnail complete"));
  }
  
  @ExceptionHandler({UploaderException.class})
  public ResponseEntity<UploadResponse> uploaderExceptionHandler(UploaderException ex) {
    logger.error(ex.getMessage());
    UploadResponse uploadResponse = new UploadResponse(ex.getErrorCode(), ex.getMessage());
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(uploadResponse);
  }
}
