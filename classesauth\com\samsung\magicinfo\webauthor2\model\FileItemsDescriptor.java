package classes.com.samsung.magicinfo.webauthor2.model;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.samsung.magicinfo.webauthor2.model.svg.MediaSource;
import java.util.List;

public class FileItemsDescriptor {
  private List<MediaSource> mediaSources;
  
  private String xml;
  
  private String playerType;
  
  private int playTime;
  
  private int width;
  
  private int height;
  
  @JsonCreator
  public FileItemsDescriptor(@JsonProperty(value = "mediaSources", required = true) List<MediaSource> mediaSources, @JsonProperty(value = "xml", required = true) String xml, @JsonProperty(value = "playerType", required = false) String playerType, @JsonProperty(value = "playTime", required = false) int playTime, @JsonProperty(value = "width", required = false) int width, @JsonProperty(value = "height", required = false) int height) {
    this.mediaSources = mediaSources;
    this.xml = xml;
    this.playerType = playerType;
    this.playTime = playTime;
    this.width = width;
    this.height = height;
  }
  
  public List<MediaSource> getMediaSources() {
    return this.mediaSources;
  }
  
  public void setMediaSources(List<MediaSource> mediaSources) {
    this.mediaSources = mediaSources;
  }
  
  public String getXml() {
    return this.xml;
  }
  
  public void setXml(String xml) {
    this.xml = xml;
  }
  
  public String getPlayerType() {
    return this.playerType;
  }
  
  public void setPlayerType(String playerType) {
    this.playerType = playerType;
  }
  
  public int getPlayTime() {
    return this.playTime;
  }
  
  public void setPlayTime(int playTime) {
    this.playTime = playTime;
  }
  
  public int getWidth() {
    return this.width;
  }
  
  public void setWidth(int width) {
    this.width = width;
  }
  
  public int getHeight() {
    return this.height;
  }
  
  public void setHeight(int height) {
    this.height = height;
  }
}
