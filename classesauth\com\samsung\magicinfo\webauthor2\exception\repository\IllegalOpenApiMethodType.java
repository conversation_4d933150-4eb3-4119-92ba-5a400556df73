package classes.com.samsung.magicinfo.webauthor2.exception.repository;

import com.samsung.magicinfo.webauthor2.exception.WebAuthorAbstractException;

public class IllegalOpenApiMethodType extends WebAuthorAbstractException {
  private static final long serialVersionUID = 8722292091543641948L;
  
  public static final String MESSAGE = "Unallowed OpenApi method type";
  
  public IllegalOpenApiMethodType(String message) {
    super(message);
  }
  
  public IllegalOpenApiMethodType(int errorCode, String message) {
    super(errorCode, message);
  }
}
